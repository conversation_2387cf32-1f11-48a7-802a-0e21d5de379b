Here's a breakdown of SDXL prompt tags and syntax:
1. Prompt structure

    Natural Language: SDXL is better at understanding natural language descriptions compared to previous versions, so you can describe your desired image in a more conversational way.
    Keywords: While natural language works well, using keywords and phrases is still a powerful method, particularly when combined with weighting and specific details.
    Subject: Focus on clearly defining the main subject of your image.
    Detailed Imagery: Add specific details about clothing, expression, color, texture, proportions, perspective, and interactions to enrich your image.
    Environment Description: Describe the setting in detail, including whether it's indoors or outdoors, and other relevant aspects.
    Mood/Atmosphere: Convey the desired emotional tone or atmosphere of the image.
    Style: Specify the artistic style or visual aesthetic, for example, "Neon Punk style" or "Fantasy art".
    Style Execution: Detail how the chosen style should be implemented, such as "utilizing vibrant neon colors and sharp contrasts". 

2. Weighting words and phrases

    Parentheses (): Increase the importance or "weight" of a word or phrase within your prompt.
        Example: (smiling:1.1) makes the smile more prominent.
        You can also nest parentheses for even greater weight: (((smiling))).
    Numerical Weighting: Specify a numerical weight using a colon : within parentheses, for example, (word:1.3) increases the weight by 30%. It's generally advised to keep weights below 1.5 to avoid distorting the image.
    Square Brackets []: Decrease the weight of the enclosed word or phrase. In some implementations, it can also be used for creating regions within a prompt. 

3. Separators and delimiters

    Comma ,: Acts as a soft separator between tokens, maintaining a degree of coherence between them. This is the most common delimiter.
    Dot .: Acts as a hard separator, preventing the AI from strongly linking tokens together, useful for long prompts with distinct elements. 

4. Other syntax and considerations

    AND or BREAK: These commands can be used to start a new "chunk" of the prompt, particularly useful for dividing regions in tools like Regional Prompter.
    Negative Prompts: Use negative prompts to specify elements you want to avoid in your image. In some implementations, you can use square brackets [] to exclude specific descriptors.
    Lora Tags: Lora tags are used for fine-tuning and adding specific details. You can insert them into the prompt to enhance the output with additional conditioning elements.
    Embeddings: Insert embeddings (pre-trained vectors) to add semantic meaning and contextual information to your prompt.
    Target Width/Height: You can specify the desired width and height of the generated image. 

Important notes

    Experimentation is key: The best way to master SDXL prompting is through practice and experimentation with different prompt structures, weighting, and stylistic choices.
    Consult Specific Implementations: The exact syntax and available features might vary slightly depending on the Stable Diffusion XL implementation you're using (e.g., Automatic1111, ComfyUI, etc.). 
blurry, low quality, distorted, deformed, disfigured, bad anatomy, artificial, fake looking, oversaturated, cartoon, anime, illustration, painting, drawing, text, watermark



demon
devil
eldritch
beast
elemental
machine
fey
golem
insect
lich
dragon
elemental
fire
ice
earth
stone
wind
lightning
tentacle
angel
giant
undead


Anna
Ariel
Aurora
Belle
Cinderella
Elsa
Asha
Jasmine
Merida
Moana
Mulan
Namaari
Pocahontas
Rapunzel
Raya
Shank
Snow White
Tiana
Vanellope
Alice
Chel
Esmerada
Jane Porter
Kida
Megera
Mirabel Madrigal
Isabela Madrigal
Dolores Madrigal
AGES :

infant
toddler
child
teenager
young adult
adult
middle-aged
senior
elderly
1 year old
2 year old
3 year old
4 year old
5 year old
6 year old
7 year old
8 year old
9 year old
10 year old
11 year old
12 year old
13 year old
14 year old
15 year old
16 year old
17 year old
18 year old
19 year old
20 year old
21 year old
22 year old
23 year old
24 year old
25 year old
26 year old
27 year old
28 year old
29 year old
30 year old
31 year old
32 year old
33 year old
34 year old
35 year old
36 year old
37 year old
38 year old
39 year old
40 year old
41 year old
42 year old
43 year old
44 year old
45 year old
46 year old
47 year old
48 year old
49 year old
50 year old
51 year old
52 year old
53 year old
54 year old
55 year old
56 year old
57 year old
58 year old
59 year old
60 year old
61 year old
62 year old
63 year old
64 year old
65 year old
66 year old
67 year old
68 year old
69 year old
70 year old
71 year old
72 year old
73 year old
74 year old
75 year old
76 year old
77 year old
78 year old
79 year old
80 year old
81 year old
82 year old
83 year old
84 year old
85 year old
86 year old
87 year old
88 year old
89 year old
90 year old
91 year old
92 year old
93 year old
94 year old
95 year old
96 year old
97 year old
98 year old
99 year old
100 year old



RACES :

Afghan
Albanian
Algerian
Andorran
Angolan
Antiguan
Argentinian
Armenian
Australian
Austrian
Azerbaijani
Bahamian
Bahraini
Bangladeshi
Barbadian
Belarusian
Belgian
Belizean
Beninese
Bhutanese
Bolivian
Bosnian
Botswanan
Brazilian
Bruneian
Bulgarian
Burkinabe
Burundian
Cambodian
Cameroonian
Canadian
Chadian
Chilean
Chinese
Colombian
Congolese
Costa Rican
Croatian
Cuban
Cypriot
Czech
Danish
Dominican
Ecuadorian
Egyptian
Salvadoran
Estonian
Ethiopian
Fijian
Finnish
French
Gabonese
Gambian
Georgian
German
Ghanaian
Greek
Guatemalan
Guinean
Guyanese
Haitian
Honduran
Hungarian
Icelandic
Indian
Indonesian
Iranian
Iraqi
Irish
Israeli
Italian
Jamaican
Japanese
Jordanian
Kazakh
Kenyan
Korean
Kuwaiti
Kyrgyz
Laotian
Latvian
Lebanese
Liberian
Libyan
Lithuanian
Luxembourgish
Malagasy
Malawian
Malaysian
Maldivian
Malian
Maltese
Mauritanian
Mexican
Moldovan
Mongolian
Moroccan
Mozambican
Burmese
Namibian
Nepalese
Dutch
New Zealander
Nicaraguan
Nigerian
Norwegian
Omani
Pakistani
Panamanian
Filipino
Polish
Portuguese
Qatari
Romanian
Russian
Rwandan
Saudi Arabian
Senegalese
Serbian
Singaporean
Slovak
Slovenian
Somali
South African
Spanish
Sri Lankan
Sudanese
Swedish
Swiss
Syrian
Taiwanese
Tajik
Tanzanian
Thai
Togolese
Tunisian
Turkish
Turkmen
Ugandan
Ukrainian
Emirati
British
American
Uruguayan
Uzbek
Venezuelan
Vietnamese
Yemeni
Zambian
Zimbabwean
Native American
Pacific Islander
Anglo
CUS5
CUS5
mixed CUS6 and CUS7
mixed CUS6 CUS7 and CUS8
mixed CUS6 and CUS7
mixed CUS6 CUS7 and CUS8

CUS4

Wood Elf
High Elf
Dark Elf
Orc
Goblin
Troll
Dragon
Centaur
Minotaur
Merfolk
Djinn
Genie
Satyr
Harpy
Gorgon
Titan
Naga
Phoenix
Sphinx
Kitsune
Dryad
Vampire
Werewolf
Fae
Celestial
Kraken
Leviathan
Hydra
Behemoth
Pixie
Sprite
Banshee
Selkie
Siren
Nymph
Unicorn
Gryphon
Chimera
Wendigo
Skinwalker
Yokai
Tengu
Oni
Kappa
Sylph
Undine
Salamander
Gnome
Brownie
Leprechaun
Changeling
Will-o-Wisp
Mothman
Jersey Devil
Chupacabra
Bigfoot
Yeti
Loch Ness Monster
Flatwoods Monster
Dover Demon
Hopkinsville Goblin
Thunderbird
Beast of Bodmin Moor
Basilisk
Cockatrice
Manticore
Pegasus
Cerberus
Cyclops
Mimic
Gargoyle
Golem
Ifrit
Rakshasa
Valkyrie
Succubus
Incubus
Wraith
Lich
Draugr
Revenant
Lamia
Medusa
Harpy
Sphynx
Fenrir
Jormungandr
Sleipnir
Tanuki
Bakeneko
Roc
Simurgh
Hippogriff
Kelpie
Nuckelavee
Beholder
Dragon Turtle
Wyvern
Amphisbaena
Ouroboros
Nemean Lion
Hippocamp
Peryton
Empusa
Grootslang
Akaname
Bunyip
Adlet
Strigoi
Anubis
Bastet
Ra
Isis
Osiris
Horus
Thoth
Sekhmet
Set
Hathor
Nephthys
Ptah
Sobek
Taweret
Khnum
Khonsu
Amun
Atum
Zeus
Hera
Poseidon
Athena
Apollo
Artemis
Ares
Aphrodite
Hephaestus
Hermes
Dionysus
Demeter
Hades
Persephone
Hestia
Nike
Hecate
Nemesis
Jupiter
Juno
Neptune
Minerva
Mars
Venus
Vulcan
Mercury
Bacchus
Ceres
Pluto
Diana
Vesta
Victoria
Bellona
Saturn
Luna
Sol
Odin
Thor
Loki
Freya
Frigg
Tyr
Heimdall
Baldur
Hel
Bragi
Idun
Njord
Skadi
Amaterasu
Susanoo
Tsukuyomi
Inari
Tenjin
Benzaiten
Vishnu
Shiva
Brahma
Ganesha
Kali
Lakshmi
Hanuman
Durga
Parvati



SHOT STYLES :

Face Shot
close-up shot
extreme close-up shot
medium shot
waist-up shot
mid-thigh up shot
full body shot
wide shot
from below view
low angle view
from above view
high angle view
bird's eye view
worm's eye view
dutch angle view
tilted angle view
front view
side view
profile shot
three-quarter view
from behind shot
over the shoulder shot
centered composition shot
rule of thirds view
symmetrical composition shot
diagonal composition shot
portrait style shot
candid style shot
environmental portrait shot
fashion photography style view
cinematic shot
professional photography shot
studio photography shot
editorial style shot
magazine style shot
commercial photography shot
headshot
glamour shot
dramatic lighting shot
natural lighting shot
outdoor portrait shot
indoor portrait shot


Hair Colors :

black
brown
dark brown
light brown
blonde
platinum blonde
golden blonde
strawberry blonde
red
ginger
auburn
copper
white
silver
gray
blue
purple
pink
green
teal
rainbow multicolored
ombre
highlighted
streaked
burgundy
chestnut
honey blonde
ash blonde
caramel
chocolate brown
jet black
raven black
sandy blonde
dirty blonde
russet
mahogany
hazel
rose
gold
pastel
neon
two-tone
tri-colored
frosted
bleached


BODY STYLES :

curvy
voluptuous
busty
hourglass figure
pear-shaped
apple-shaped
rectangular
tiny
petite
small
large
huge
giant
anorexic
skeletal
emaciated
morbidly obese
grotesque
thicc
swole
built
stacked
chubby
plump
pudgy
athletic
slim
skinny
thin
fit
muscular
ripped
jacked
buff
toned
average
thick
chunky
heavy
fat
massive


Emotions

happy
ecstatic
overjoyed
sad
devastated
heartbroken
angry
furious
enraged
confident
arrogant
cocky
thoughtful
contemplative
meditative
serene
peaceful
tranquil
anxious
terrified
horrified
excited
thrilled
exhilarated
determined
resolute
unwavering
melancholic
gloomy
depressed
joyful
blissful
elated
curious
inquisitive
fascinated
proud
triumphant
victorious
fearful
panicked
petrified
amused
delighted
entertained
surprised
shocked
stunned
passionate
intense
zealous
worried
distressed
anguished
hysterical

Eyes:

black
brown
dark brown
light brown
platinum
red
auburn
copper
white
silver
gray
blue
purple
pink
green
teal
rose
pastel
neon
two-tone
tri-colored
frosted
blind


Custom Descriptors

tall
short
average height
petite
statuesque
slender
athletic
curvy
full-figured
lean
willowy
stocky
muscular
delicate
robust
graceful
elegant
plain
beautiful
striking
ethereal
homely
weathered
rugged
dainty
stout
lanky
gaunt
plump
chubby
thin
wiry
broad
compact
towering
diminutive
angular
round-faced
square-jawed
sharp-featured
soft-featured
stern-looking
gentle-looking
fierce
timid
commanding
meek
imposing
unassuming
youthful
mature
aged
