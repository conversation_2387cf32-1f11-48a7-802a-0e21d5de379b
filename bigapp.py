
import torch
import os

# Aggressive memory optimization for 6GB cards
if torch.cuda.is_available():
    # Set memory fraction to leave some headroom
    torch.cuda.set_per_process_memory_fraction(0.95)  # Use 95% of VRAM max

    # Enable memory efficient attention
    torch.backends.cuda.enable_flash_sdp(False)  # Disable flash attention if causing issues

    # Set CUDA memory allocation strategy
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
import subprocess
import sys
import logging
import os
import traceback
from datetime import datetime
import huggingface_hub
import transformers
from diffusers import (
    logging as diffusers_logging,
    EulerDiscreteScheduler,
    StableDiffusionXLPipeline,
    AutoPipelineForImage2Image
)
from fastapi import (
    FastAPI,
    UploadFile,
    File,
    Form,
    HTTPException,
    BackgroundTasks,
    Request,
    Response
)
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from safetensors.torch import load_file
from PIL import Image
import io
import cv2
import numpy as np
import uuid
import json
import signal
import random
import gc
from accelerate import cpu_offload
from torch.cuda.amp import autocast
from transformers import CLIPTokenizer
import asyncio
from typing import Dict, Optional

# Single definition of setup_all_loggers
def setup_all_loggers():
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Configure root logger to not show debug messages
    logging.basicConfig(level=logging.INFO)
    
    # Common formatter
    formatter = logging.Formatter('%(asctime)s | %(name)s | %(levelname)s | %(message)s')
    
    # Setup loaded logger
    loaded_logger = logging.getLogger('loaded_logger')
    loaded_logger.setLevel(logging.INFO)
    fh_loaded = logging.FileHandler('logs/loaded.txt', encoding='utf-8')
    fh_loaded.setFormatter(formatter)
    loaded_logger.addHandler(fh_loaded)
    # Remove console handler for loaded_logger
    
    # Setup usage logger
    usage_logger = logging.getLogger('usage_logger')
    usage_logger.setLevel(logging.INFO)
    fh_usage = logging.FileHandler('logs/usage.txt', encoding='utf-8')
    fh_usage.setFormatter(formatter)
    usage_logger.addHandler(fh_usage)
    
    # Setup passed logger
    passed_logger = logging.getLogger('passed_logger')
    passed_logger.setLevel(logging.INFO)
    fh_passed = logging.FileHandler('logs/passed.txt', encoding='utf-8')
    fh_passed.setFormatter(formatter)
    passed_logger.addHandler(fh_passed)
    
    # Setup debug logger - only file handler, no console output
    debug_logger = logging.getLogger('debug_logger')
    debug_logger.setLevel(logging.DEBUG)
    debug_logger.propagate = False  # Prevent propagation to root logger
    fh_debug = logging.FileHandler('logs/debug.txt', encoding='utf-8')
    fh_debug.setFormatter(formatter)
    debug_logger.addHandler(fh_debug)
    
    # Fix the flush function to handle None case
    def safe_flush(handler):
        if handler.stream and hasattr(handler.stream, 'fileno'):
            try:
                os.fsync(handler.stream.fileno())
            except (IOError, ValueError):
                pass

    # Force immediate writes for all file handlers
    for handler in [fh_loaded, fh_usage, fh_passed, fh_debug]:
        handler.flush = lambda h=handler: safe_flush(h)
    
    return loaded_logger, usage_logger, passed_logger, debug_logger

# Single initialization of loggers
loaded_logger, usage_logger, passed_logger, debug_logger = setup_all_loggers()

# Add a test log to verify logging is working
loaded_logger.info("=== Logging System Initialized ===")
debug_logger.debug("Debug logging system initialized")
usage_logger.info("Usage logging system initialized")
passed_logger.info("Passed logging system initialized")

# Try importing required packages, install if missing
def ensure_package(package_name, version):
    try:
        __import__(package_name)
    except ImportError:
        loaded_logger.info(f"Installing {package_name}=={version}...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install",
            f"{package_name}=={version}",
            "--no-deps"
        ])

# Ensure core dependencies in the correct order
ensure_package("huggingface_hub", "0.21.3")  # Updated version
ensure_package("transformers", "4.36.2")
ensure_package("diffusers", "0.21.4")  # Updated version
ensure_package("accelerate", "0.25.0")
ensure_package("peft", "0.6.2")

# Set logging before other imports to suppress warnings
diffusers_logging.set_verbosity_error()
transformers.logging.set_verbosity_error()

# Check huggingface_hub version and downgrade if needed
current_version = huggingface_hub.__version__
if current_version > "0.19.4":
    loaded_logger.info("Downgrading huggingface_hub to compatible version...")
    subprocess.check_call([
        sys.executable, "-m", "pip", "install",
        "huggingface-hub==0.19.4",
        "--no-deps",
        "--force-reinstall"
    ])
    print("Please restart the application")
    sys.exit(0)

# Now import diffusers after ensuring compatible versions
import diffusers
from fastapi import FastAPI, UploadFile, File, Form, HTTPException, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
# Import diffusers components with error handling
try:
    from diffusers import StableDiffusionXLPipeline, AutoPipelineForImage2Image
    from diffusers import DDIMScheduler

    # Try to import SVD - this is the only video feature we need
    try:
        from diffusers import StableVideoDiffusionPipeline
        SVD_AVAILABLE = True
        print("SVD (Stable Video Diffusion) available")
    except ImportError:
        print("Warning: StableVideoDiffusionPipeline not available in this diffusers version")
        SVD_AVAILABLE = False
        StableVideoDiffusionPipeline = None

    # We don't need AnimateDiff for image generation
    ANIMATEDIFF_AVAILABLE = False
    AnimateDiffPipeline = None
    MotionAdapter = None

except ImportError as e:
    print(f"Error importing diffusers: {e}")
    raise
from safetensors.torch import load_file
from PIL import Image
import io
import cv2
import numpy as np
import uuid
import os
import json
import signal
import random
import gc
from accelerate import cpu_offload
from torch.cuda.amp import autocast
from transformers import CLIPTokenizer
from fastapi import FastAPI, Request, Response  # Remove Request, Response
import asyncio
from typing import Dict, Optional
import queue  # Remove this import
import threading  # Remove if not used elsewhere

def setup_all_loggers():
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Configure root logger to not show debug messages
    logging.basicConfig(level=logging.INFO)
    
    # Common formatter
    formatter = logging.Formatter('%(asctime)s | %(name)s | %(levelname)s | %(message)s')
    
    # Setup loaded logger
    loaded_logger = logging.getLogger('loaded_logger')
    loaded_logger.setLevel(logging.INFO)
    fh_loaded = logging.FileHandler('logs/loaded.txt', encoding='utf-8')
    fh_loaded.setFormatter(formatter)
    loaded_logger.addHandler(fh_loaded)
    # Remove console handler for loaded_logger
    
    # Setup usage logger
    usage_logger = logging.getLogger('usage_logger')
    usage_logger.setLevel(logging.INFO)
    fh_usage = logging.FileHandler('logs/usage.txt', encoding='utf-8')
    fh_usage.setFormatter(formatter)
    usage_logger.addHandler(fh_usage)
    
    # Setup passed logger
    passed_logger = logging.getLogger('passed_logger')
    passed_logger.setLevel(logging.INFO)
    fh_passed = logging.FileHandler('logs/passed.txt', encoding='utf-8')
    fh_passed.setFormatter(formatter)
    passed_logger.addHandler(fh_passed)
    
    # Setup debug logger - only file handler, no console output
    debug_logger = logging.getLogger('debug_logger')
    debug_logger.setLevel(logging.DEBUG)
    debug_logger.propagate = False  # Prevent propagation to root logger
    fh_debug = logging.FileHandler('logs/debug.txt', encoding='utf-8')
    fh_debug.setFormatter(formatter)
    debug_logger.addHandler(fh_debug)
    
    # Fix the flush function to handle None case
    def safe_flush(handler):
        if handler.stream and hasattr(handler.stream, 'fileno'):
            try:
                os.fsync(handler.stream.fileno())
            except (IOError, ValueError):
                pass

    # Force immediate writes for all file handlers
    for handler in [fh_loaded, fh_usage, fh_passed, fh_debug]:
        handler.flush = lambda h=handler: safe_flush(h)
    
    return loaded_logger, usage_logger, passed_logger, debug_logger

# Initialize all loggers immediately
loaded_logger, usage_logger, passed_logger, debug_logger = setup_all_loggers()

# Add a test log to verify logging is working
loaded_logger.info("=== Logging System Initialized ===")
debug_logger.debug("Debug logging system initialized")
usage_logger.info("Usage logging system initialized")
passed_logger.info("Passed logging system initialized")

# Check CUDA availability and setup optimizations
device = "cuda" if torch.cuda.is_available() else "cpu"
if device == "cuda":
    try:
        torch.backends.cudnn.benchmark = True
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        torch.cuda.set_per_process_memory_fraction(0.95)
    except Exception as e:
        debug_logger.warning(f"Could not set CUDA optimizations: {str(e)}")

# Create required directories
os.makedirs("static/outputs", exist_ok=True)
os.makedirs("sdxl/configs", exist_ok=True)

bigapp = FastAPI()
bigapp.mount("/static", StaticFiles(directory="static"), name="static")
bigapp.mount("/output", StaticFiles(directory="output"), name="output")

# Global variables
txt2img_pipe = None
img2img_pipe = None
svd_pipe = None
animatediff_pipe = None
model_path = "./sdxl"
CURRENT_CHECKPOINT = None

def get_txt2img_pipeline():
    global txt2img_pipe, CURRENT_CHECKPOINT
    try:
        print("\n[DEBUG] Entering get_txt2img_pipeline()")
        print(f"[DEBUG] Current txt2img_pipe state: {'None' if txt2img_pipe is None else 'Loaded'}")
        print(f"[DEBUG] Current checkpoint: {CURRENT_CHECKPOINT}")
        
        if txt2img_pipe is None:
            print("[DEBUG] Pipeline is None, initializing new pipeline...")
            loaded_logger.info("=== Loading Text2Image Pipeline ===")
            
            # Create Euler scheduler
            print("[DEBUG] Creating Euler scheduler...")
            scheduler = EulerDiscreteScheduler(
                beta_start=0.00085,
                beta_end=0.012,
                beta_schedule="scaled_linear",
                timestep_spacing="leading",
                steps_offset=1,
                num_train_timesteps=3201,  # Increased to support up to 3200 steps
                use_karras_sigmas=True
            )
            
            if CURRENT_CHECKPOINT and os.path.exists(CURRENT_CHECKPOINT):
                print(f"[DEBUG] Loading from checkpoint: {CURRENT_CHECKPOINT}")
                loaded_logger.info(f"Loading from checkpoint: {CURRENT_CHECKPOINT}")
                txt2img_pipe = load_checkpoint(CURRENT_CHECKPOINT)
            else:
                print(f"[DEBUG] Loading base model from: {model_path}")
                loaded_logger.info(f"Loading base model from: {model_path}")
                
                if not os.path.exists(model_path):
                    print(f"[ERROR] Base model path not found: {model_path}")
                    raise FileNotFoundError(f"Base model path not found: {model_path}")
                    
                print("[DEBUG] Initializing StableDiffusionXLPipeline...")
                txt2img_pipe = StableDiffusionXLPipeline.from_pretrained(
                    model_path,
                    torch_dtype=torch.float16,
                    scheduler=scheduler,
                    local_files_only=True,
                    requires_safety_checker=False,
                    safety_checker=None,
                    feature_extractor=None,
                    add_watermarker=False
                )
                
                if torch.cuda.is_available():
                    print("[DEBUG] CUDA available, moving pipeline to GPU...")
                    txt2img_pipe = txt2img_pipe.to("cuda")
                    print("[DEBUG] Enabling model optimizations...")
                    txt2img_pipe.enable_model_cpu_offload()
                    txt2img_pipe.enable_vae_slicing()
                    txt2img_pipe.enable_attention_slicing()
                    try:
                        txt2img_pipe.enable_xformers_memory_efficient_attention()
                        print("[DEBUG] xformers optimization enabled")
                    except Exception as e:
                        print(f"[WARNING] Could not enable xformers: {str(e)}")
                        loaded_logger.warning(f"Could not enable xformers: {str(e)}")
                        
            print("[DEBUG] Pipeline initialization completed")
            loaded_logger.info("Text2Image Pipeline loaded successfully")
            loaded_logger.info("-------------------")
            loaded_logger.handlers[0].flush()
            
        else:
            print("[DEBUG] Using existing pipeline instance")
            
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**2
            memory_reserved = torch.cuda.memory_reserved() / 1024**2
            print(f"[DEBUG] CUDA Memory Status:")
            print(f"[DEBUG] - Allocated: {memory_allocated:.2f}MB")
            print(f"[DEBUG] - Reserved: {memory_reserved:.2f}MB")
            
        return txt2img_pipe
        
    except Exception as e:
        print(f"[ERROR] Pipeline initialization failed: {str(e)}")
        print(f"[ERROR] Traceback: {traceback.format_exc()}")
        loaded_logger.error(f"Failed to initialize pipeline: {str(e)}")
        loaded_logger.error(f"Traceback: {traceback.format_exc()}")
        raise RuntimeError(f"Failed to initialize pipeline: {str(e)}")

def get_img2img_pipeline():
    global img2img_pipe
    if img2img_pipe is None:
        loaded_logger.info("=== Loading Image2Image Pipeline ===")
        loaded_logger.info(f"Loading from: {model_path}")
        loaded_logger.handlers[0].flush()
        try:
            from diffusers import EulerDiscreteScheduler
            
            scheduler = EulerDiscreteScheduler(
                beta_start=0.00085,
                beta_end=0.012,
                beta_schedule="scaled_linear",
                timestep_spacing="leading",
                steps_offset=1,
                num_train_timesteps=3201,  # Increased to support up to 3200 steps
                use_karras_sigmas=True
            )
            
            img2img_pipe = AutoPipelineForImage2Image.from_pretrained(
                model_path,
                torch_dtype=torch.float16,
                local_files_only=True,
                scheduler=scheduler,
                variant="fp16",
                use_safetensors=True,
                requires_safety_checker=False,
                safety_checker=None,
                feature_extractor=None,
                add_watermarker=False
            ).to(device)

            if device == "cuda":
                img2img_pipe.enable_model_cpu_offload()
                img2img_pipe.enable_vae_slicing()
                img2img_pipe.enable_attention_slicing(1)
                try:
                    img2img_pipe.enable_xformers_memory_efficient_attention()
                except:
                    pass
                torch.cuda.empty_cache()
                gc.collect()
            
        except Exception as e:
            loaded_logger.error(f"Failed to load Image2Image Pipeline: {str(e)}")
            loaded_logger.info("-------------------")
            loaded_logger.handlers[0].flush()
            raise
        loaded_logger.info("Image2Image Pipeline loaded successfully")
        loaded_logger.info("-------------------")
        loaded_logger.handlers[0].flush()
    return img2img_pipe

def get_svd_pipeline():
    global svd_pipe
    if not SVD_AVAILABLE:
        raise HTTPException(status_code=500, detail="SVD not available in this diffusers version. Please update diffusers to 0.25.0+")

    if svd_pipe is None:
        loaded_logger.info("=== Loading Stable Video Diffusion Pipeline ===")
        loaded_logger.handlers[0].flush()
        try:
            svd_pipe = StableVideoDiffusionPipeline.from_pretrained(
                "stabilityai/stable-video-diffusion-img2vid-xt",
                torch_dtype=torch.float16,
                variant="fp16"
            )

            if torch.cuda.is_available():
                svd_pipe.to(device)

                # Try memory optimization methods - some may not exist in all versions
                try:
                    svd_pipe.enable_model_cpu_offload()
                except AttributeError:
                    loaded_logger.info("enable_model_cpu_offload not available")

                try:
                    svd_pipe.enable_vae_slicing()
                except AttributeError:
                    loaded_logger.info("enable_vae_slicing not available")

                try:
                    svd_pipe.enable_xformers_memory_efficient_attention()
                except AttributeError:
                    loaded_logger.info("enable_xformers_memory_efficient_attention not available")

            loaded_logger.info("SVD Pipeline loaded successfully")
            loaded_logger.info("-------------------")
            loaded_logger.handlers[0].flush()

        except Exception as e:
            loaded_logger.error(f"Failed to load SVD Pipeline: {str(e)}")
            loaded_logger.info("-------------------")
            loaded_logger.handlers[0].flush()
            raise

    return svd_pipe

# AnimateDiff removed - not needed for image generation

def save_generation_metadata(metadata):
    """Save generation metadata to master JSON file"""
    metadata_file = "generations.json"

    # Load existing metadata or create new list
    try:
        if os.path.exists(metadata_file):
            with open(metadata_file, 'r', encoding='utf-8') as f:
                generations = json.load(f)
        else:
            generations = []
    except Exception as e:
        loaded_logger.error(f"Error loading metadata file: {e}")
        generations = []

    # Add new generation
    generations.append(metadata)

    # Save updated metadata
    try:
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(generations, f, indent=2, ensure_ascii=False)
        loaded_logger.info(f"Metadata saved to {metadata_file}")
    except Exception as e:
        loaded_logger.error(f"Error saving metadata: {e}")

@bigapp.get("/")
async def read_root():
    return FileResponse("static/index.html")

@bigapp.get("/generations.json")
async def get_generations_json():
    """Serve the generations.json file directly"""
    if os.path.exists("generations.json"):
        return FileResponse("generations.json", media_type="application/json")
    else:
        # Return empty array if file doesn't exist
        from fastapi.responses import JSONResponse
        return JSONResponse(content=[])

@bigapp.get("/animation_status")
async def get_animation_status():
    """Check which animation methods are available"""
    return {
        "svd_available": SVD_AVAILABLE,
        "animatediff_available": ANIMATEDIFF_AVAILABLE,
        "diffusers_version": diffusers.__version__ if 'diffusers' in globals() else "unknown"
    }

# Backend search endpoints removed - frontend handles JSON file directly

def validate_lora_file(lora_path):
    if not lora_path.endswith(('.safetensors', '.bin')):
        raise ValueError(f"Invalid LoRA file format: {lora_path}")
    return True

def get_tokenizer():
    global txt2img_pipe
    if txt2img_pipe is None:
        # Initialize the pipeline if it's not already initialized
        txt2img_pipe = get_txt2img_pipeline()
    
    # SDXL uses two tokenizers, we'll use the first one for token counting
    return txt2img_pipe.tokenizer

@bigapp.post("/count_tokens")
async def count_tokens(request: Request):
    data = await request.json()
    text = data.get("text", "")
    
    tokenizer = get_tokenizer()
    tokens = tokenizer.encode(text)
    token_count = len(tokens)
    
    return {"token_count": token_count}

@bigapp.post("/generate")
async def generate_image(
    request: Request,
    prompt_1: str = Form(...),  # Primary prompt
    prompt_2: str = Form(...),  # Secondary prompt
    loras: str = Form("[]"),
    negative_prompt: str = Form(""),
    width: int = Form(...),
    height: int = Form(...),
    inference_steps: int = Form(...),
    prompt_strength: float = Form(...),
    seed: int = Form(-1),  # -1 for random, otherwise fixed seed
    template_image: UploadFile = File(None),
    template_strength: float = Form(None),
    batch_enabled: bool = Form(False),
    batch_count: int = Form(1)
):
    debug_info = []
    pipeline = None
    try:
        # Validate input parameters
        if width <= 0 or height <= 0:
            raise ValueError("Width and height must be positive values")
        if inference_steps <= 0:
            raise ValueError("Inference steps must be positive")
        if prompt_strength <= 0:
            raise ValueError("Prompt strength must be positive")
            
        debug_logger.info("\n=== Starting new generation request ===")
        debug_logger.info(f"Primary Prompt: {prompt_1}")
        debug_logger.info(f"Secondary Prompt: {prompt_2}")
        debug_logger.info(f"Parameters: width={width}, height={height}, steps={inference_steps}, strength={prompt_strength}")
        
        # Initialize pipeline
        try:
            pipeline = get_txt2img_pipeline() if not template_image else get_img2img_pipeline()
            if pipeline is None:
                raise RuntimeError("Failed to initialize pipeline - pipeline is None")
        except Exception as pipe_error:
            debug_logger.error(f"Pipeline initialization failed: {str(pipe_error)}")
            debug_logger.error(f"Traceback:\n{traceback.format_exc()}")
            raise RuntimeError(f"Pipeline initialization failed: {str(pipe_error)}")

        # Load LoRAs if any
        lora_data = json.loads(loras)
        debug_logger.info(f"Parsed LoRA data: {json.dumps(lora_data, indent=2)}")
        
        if lora_data:
            debug_logger.info("\n=== Loading LoRAs ===")
            try:
                debug_logger.info("Before loading LoRAs - Memory status:")
                if torch.cuda.is_available():
                    memory_allocated = torch.cuda.memory_allocated() / 1024**2
                    memory_reserved = torch.cuda.memory_reserved() / 1024**2
                    debug_logger.info(f"Allocated: {memory_allocated:.2f}MB")
                    debug_logger.info(f"Reserved: {memory_reserved:.2f}MB")
                
                pipeline = load_multiple_loras(pipeline, lora_data)
                debug_logger.info("LoRAs loaded successfully")
                
                debug_logger.info("After loading LoRAs - Memory status:")
                if torch.cuda.is_available():
                    memory_allocated = torch.cuda.memory_allocated() / 1024**2
                    memory_reserved = torch.cuda.memory_reserved() / 1024**2
                    debug_logger.info(f"Allocated: {memory_allocated:.2f}MB")
                    debug_logger.info(f"Reserved: {memory_reserved:.2f}MB")
            except Exception as lora_error:
                debug_logger.error("\n=== LoRA Loading Failed ===")
                debug_logger.error(f"Error type: {type(lora_error).__name__}")
                debug_logger.error(f"Error message: {str(lora_error)}")
                debug_logger.error(f"Traceback:\n{traceback.format_exc()}")
                raise

        # Token counting and logging for main prompts
        tokenizer_1 = pipeline.tokenizer
        tokenizer_2 = pipeline.tokenizer_2

        passed_logger.info("\n=== Generation Details ===")
        passed_logger.info("Original Prompts:")
        passed_logger.info(f"Prompt 1: {prompt_1}")
        passed_logger.info(f"Prompt 2: {prompt_2}")
        if negative_prompt:
            passed_logger.info(f"Negative Prompt: {negative_prompt}")

        # First Encoder
        tokens_1 = tokenizer_1.encode(prompt_1)
        processed_tokens_1 = tokenizer_1.decode(tokens_1[:77])
        passed_logger.info(f"\nEncoder 1:")
        passed_logger.info(f"Total tokens: {len(tokens_1)}")
        passed_logger.info(f"Tokens used: {min(len(tokens_1), 77)}")
        passed_logger.info(f"Processed text: {processed_tokens_1}")

        # Second Encoder
        tokens_2 = tokenizer_2.encode(prompt_2)
        processed_tokens_2 = tokenizer_2.decode(tokens_2[:77])
        passed_logger.info(f"\nEncoder 2:")
        passed_logger.info(f"Total tokens: {len(tokens_2)}")
        passed_logger.info(f"Tokens used: {min(len(tokens_2), 77)}")
        passed_logger.info(f"Processed text: {processed_tokens_2}")

        if negative_prompt:
            # Negative prompt processing for both encoders
            neg_tokens_1 = tokenizer_1.encode(negative_prompt)
            neg_processed_1 = tokenizer_1.decode(neg_tokens_1[:77])
            neg_tokens_2 = tokenizer_2.encode(negative_prompt)
            neg_processed_2 = tokenizer_2.decode(neg_tokens_2[:77])
            
            passed_logger.info(f"\nNegative Prompt Processing:")
            passed_logger.info(f"Encoder 1 (tokens: {min(len(neg_tokens_1), 77)}/{len(neg_tokens_1)}): {neg_processed_1}")
            passed_logger.info(f"Encoder 2 (tokens: {min(len(neg_tokens_2), 77)}/{len(neg_tokens_2)}): {neg_processed_2}")

        # Handle seed
        if seed == -1:
            actual_seed = random.randint(0, 2**32 - 1)
        else:
            actual_seed = seed

        generator = torch.Generator(device=device).manual_seed(actual_seed)

        passed_logger.info("\n=== Generation Parameters ===")
        passed_logger.info(f"Width: {width}, Height: {height}")
        passed_logger.info(f"Steps: {inference_steps}, Strength: {prompt_strength}")
        passed_logger.info(f"Seed: {actual_seed}")
        passed_logger.info("=" * 50 + "\n")

        # Generate image
        debug_logger.info("\n=== Starting Generation ===")
        debug_logger.info("Generation parameters:")
        debug_logger.info(f"- Width: {width}")
        debug_logger.info(f"- Height: {height}")
        debug_logger.info(f"- Steps: {inference_steps}")
        debug_logger.info(f"- Prompt strength: {prompt_strength}")
        
        # Aggressive memory cleanup before generation (for 6GB cards)
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        gc.collect()

        try:
            if template_image:
                contents = await template_image.read()
                init_image = Image.open(io.BytesIO(contents)).convert("RGB")
                init_image = init_image.resize((width, height))
                debug_logger.info("Template image processed successfully")
                
                image = pipeline(
                    prompt=prompt_1,  # Use primary prompt
                    negative_prompt=negative_prompt,
                    image=init_image,
                    strength=template_strength,
                    num_inference_steps=inference_steps,
                    guidance_scale=prompt_strength,
                    generator=generator
                ).images[0]
            else:
                debug_logger.info("Starting txt2img generation...")
                debug_logger.info(f"Prompts being sent to pipeline:")
                debug_logger.info(f"Prompt 1: '{prompt_1}'")
                debug_logger.info(f"Prompt 2: '{prompt_2}'")

                # Try explicit dual-prompt generation with cross-attention scaling
                debug_logger.info(f"Processing with prompts:")
                debug_logger.info(f"Prompt 1: {prompt_1}")
                debug_logger.info(f"Prompt 2: {prompt_2}")

                # Combine prompts with weighting
                combined_prompt = f"{prompt_1} AND {prompt_2}"

                image = pipeline(
                    prompt=combined_prompt,
                    negative_prompt=negative_prompt,
                    num_inference_steps=inference_steps,
                    guidance_scale=prompt_strength,
                    width=width,
                    height=height,
                    output_type="pil",
                    generator=generator,
                    # Add these to try to influence both text encoders
                    target_prompt_1=prompt_1,
                    target_prompt_2=prompt_2
                ).images[0]
            debug_logger.info("Image generation completed successfully")
        except Exception as gen_error:
            debug_logger.error("\n=== Generation Process Failed ===")
            debug_logger.error(f"Error occurred during the pipeline call")
            debug_logger.error(f"Error type: {type(gen_error).__name__}")
            debug_logger.error(f"Error message: {str(gen_error)}")
            debug_logger.error(f"Traceback:\n{traceback.format_exc()}")
            raise

        # Save the image
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"output/image_{timestamp}_{actual_seed}_{prompt_strength}_{inference_steps}.png"
        debug_logger.info(f"Attempting to save image to: {output_path}")
        image.save(output_path)
        debug_logger.info(f"Image saved successfully to {output_path}")

        # Save metadata to master JSON file
        metadata = {
            "filename": os.path.basename(output_path),
            "timestamp": datetime.now().isoformat(),
            "generation_type": "img2img" if template_image else "txt2img",
            "prompt_1": prompt_1,
            "prompt_2": prompt_2,
            "negative_prompt": negative_prompt,
            "seed": actual_seed,
            "width": width,
            "height": height,
            "inference_steps": inference_steps,
            "prompt_strength": prompt_strength,
            "template_strength": template_strength if template_image else None,
            "checkpoint": CURRENT_CHECKPOINT or "base_sdxl",
            "loras": json.loads(loras) if loras != "[]" else [],
            "full_path": output_path
        }
        save_generation_metadata(metadata)

        # Aggressive cleanup after generation (for 6GB cards)
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**2
            memory_reserved = torch.cuda.memory_reserved() / 1024**2
            debug_logger.info("\n=== CUDA Memory Status ===")
            debug_logger.info(f"Allocated: {memory_allocated:.2f}MB")
            debug_logger.info(f"Reserved: {memory_reserved:.2f}MB")

            # Aggressive cleanup for 6GB cards
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
            gc.collect()
            debug_logger.info("Aggressive CUDA cleanup completed")

        return {"image_path": f"/{output_path}", "debug_info": debug_info, "seed_used": actual_seed}

    except Exception as e:
        debug_logger.error("\n=== Generation Error ===")
        debug_logger.error(f"Error type: {type(e).__name__}")
        debug_logger.error(f"Error message: {str(e)}")
        debug_logger.error(f"Traceback:\n{traceback.format_exc()}")
        raise

    except Exception as e:
        debug_logger.error("\n=== Request Failed ===")
        debug_logger.error(f"Error type: {type(e).__name__}")
        debug_logger.error(f"Error message: {str(e)}")
        debug_logger.error(f"Traceback:\n{traceback.format_exc()}")
        
        error_detail = {
            "error": str(e),
            "error_type": type(e).__name__,
            "traceback": traceback.format_exc(),
            "debug": debug_info
        }
        
        raise HTTPException(
            status_code=500, 
            detail=error_detail
        )
    finally:
        debug_logger.info("\n=== Post-generation cleanup ===")
        try:
            # Clear LoRAs but keep pipeline loaded
            if pipeline and hasattr(pipeline, 'unload_lora_weights'):
                pipeline.unload_lora_weights()
                print("LoRAs successfully unloaded")
                debug_logger.info("Successfully cleared all LoRA weights")
            else:
                print("LoRAs could not be unloaded")
                debug_logger.info("Could not unload LoRAs - pipeline or unload_lora_weights not available")
            
            # Basic memory cleanup
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                gc.collect()
                memory_allocated = torch.cuda.memory_allocated() / 1024**2
                memory_reserved = torch.cuda.memory_reserved() / 1024**2
                debug_logger.info("\n=== Final CUDA Memory Status ===")
                debug_logger.info(f"Allocated: {memory_allocated:.2f}MB")
                debug_logger.info(f"Reserved: {memory_reserved:.2f}MB")
        except Exception as e:
            print("LoRAs could not be unloaded")
            debug_logger.error(f"Post-generation cleanup failed: {str(e)}")

@bigapp.post("/animate")
async def animate_image_svd(
    file: UploadFile = File(...),
    num_frames: int = Form(25),
    motion_bucket_id: int = Form(127),
    fps: int = Form(7),
    decode_chunk_size: int = Form(8),
    seed: int = Form(-1)
):
    """
    Generate video using Stable Video Diffusion (SVD)
    """
    try:
        debug_logger.info("=== Starting SVD Animation ===")

        # Read and process the input image
        image_data = await file.read()
        init_image = Image.open(io.BytesIO(image_data)).convert("RGB")

        # SVD works best with specific resolutions, let's resize appropriately
        # SVD-XT works with 1024x576 or 576x1024
        width, height = init_image.size
        if width > height:
            # Landscape - use 1024x576
            init_image = init_image.resize((1024, 576))
        else:
            # Portrait or square - use 576x1024
            init_image = init_image.resize((576, 1024))

        debug_logger.info(f"Input image resized to: {init_image.size}")
        debug_logger.info(f"Parameters: frames={num_frames}, motion_bucket_id={motion_bucket_id}, fps={fps}")

        # Handle seed
        if seed == -1:
            actual_seed = random.randint(0, 2**32 - 1)
        else:
            actual_seed = seed

        debug_logger.info(f"Using seed: {actual_seed}")

        # Get SVD pipeline
        pipeline = get_svd_pipeline()

        # Generate video frames using SVD
        debug_logger.info("Starting SVD generation...")
        with torch.inference_mode():
            frames = pipeline(
                image=init_image,
                decode_chunk_size=decode_chunk_size,
                num_frames=num_frames,
                motion_bucket_id=motion_bucket_id,  # Controls motion intensity (1-255)
                fps=fps,
                generator=torch.Generator(device=device).manual_seed(actual_seed)
            ).frames[0]

        debug_logger.info(f"Generated {len(frames)} frames")

        # Save sample frames
        samples_dir = "static/samples"
        os.makedirs(samples_dir, exist_ok=True)

        # Save every 5th frame as sample
        for i, frame in enumerate(frames):
            if i % 5 == 0 or i == len(frames) - 1:
                sample_path = os.path.join(samples_dir, f"svd_frame_{i+1}.png")
                frame.save(sample_path)

        # Convert PIL frames to numpy arrays for video writing
        frame_arrays = [np.array(frame) for frame in frames]

        # Create output video with metadata in filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"static/outputs/svd_video_{timestamp}_{actual_seed}_{motion_bucket_id}_{num_frames}f.mp4"
        os.makedirs("static/outputs", exist_ok=True)

        height, width = frame_arrays[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        for frame_array in frame_arrays:
            frame_bgr = cv2.cvtColor(frame_array, cv2.COLOR_RGB2BGR)
            out.write(frame_bgr)

        out.release()

        debug_logger.info(f"Video saved to: {output_path}")

        # Save metadata to master JSON file
        metadata = {
            "filename": os.path.basename(output_path),
            "timestamp": datetime.now().isoformat(),
            "generation_type": "svd_animation",
            "seed": actual_seed,
            "num_frames": num_frames,
            "motion_bucket_id": motion_bucket_id,
            "fps": fps,
            "decode_chunk_size": decode_chunk_size,
            "input_image_size": f"{init_image.size[0]}x{init_image.size[1]}",
            "checkpoint": "SVD (Stable Video Diffusion)",
            "full_path": output_path
        }
        save_generation_metadata(metadata)

        # Cleanup
        frames = None
        frame_arrays = None
        torch.cuda.empty_cache()
        gc.collect()

        return {"video_path": output_path, "seed_used": actual_seed}

    except Exception as e:
        debug_logger.error(f"SVD Animation failed: {str(e)}")
        debug_logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # Cleanup
        debug_logger.info("=== Post-SVD cleanup ===")
        try:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                gc.collect()
                debug_logger.info("CUDA cache cleared")
        except Exception as e:
            debug_logger.error(f"Post-SVD cleanup failed: {str(e)}")

# AnimateDiff endpoint removed - not needed for image generation

# Keep the old animation endpoint as a fallback (rename it)
@bigapp.post("/animate_legacy")
async def animate_image_legacy(
    file: UploadFile = File(...),
    prompt_1: str = Form(...),
    prompt_2: str = Form(...),
    num_frames: int = Form(...),
    animateInferenceSteps: int = Form(...),
    strength: float = Form(...),
    framerate: int = Form(...),
    prompt_strength: float = Form(...),
    negative_prompt: str = Form(""),
    loras: str = Form("[]"),
    seed: int = Form(-1)
):
    """
    Legacy animation using iterative img2img (the old fuzzy method)
    """
    try:
        image_data = await file.read()
        init_image = Image.open(io.BytesIO(image_data)).convert("RGB")
        init_image = init_image.resize((1080, 1080))

        pipeline = get_img2img_pipeline()

        # Process LoRAs if provided
        lora_data = json.loads(loras)
        if lora_data:
            pipeline = load_multiple_loras(pipeline, lora_data)

        frames = []
        samples_dir = "static/samples"
        os.makedirs(samples_dir, exist_ok=True)

        # Handle seed
        if seed == -1:
            actual_seed = random.randint(0, 2**32 - 1)
        else:
            actual_seed = seed

        generator = torch.Generator(device=device).manual_seed(actual_seed)

        current_image = init_image
        with torch.inference_mode():
            for i in range(num_frames):
                if i > 0 and i % 5 == 0:
                    torch.cuda.empty_cache()
                    gc.collect()

                result = pipeline(
                    prompt=prompt_1,
                    negative_prompt=negative_prompt,
                    image=current_image,
                    strength=strength,
                    num_inference_steps=animateInferenceSteps,
                    generator=generator,
                    guidance_scale=prompt_strength
                ).images[0]

                frames.append(np.array(result))
                current_image = result

                if (i + 1) % 5 == 0:
                    sample_path = os.path.join(samples_dir, f"legacy_frame_{i+1}.png")
                    result.save(sample_path)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"static/outputs/legacy_video_{timestamp}_{actual_seed}_{strength}_{num_frames}f.mp4"
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, framerate, (width, height))

        for frame in frames:
            frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            out.write(frame_bgr)

        out.release()

        # Save metadata to master JSON file
        metadata = {
            "filename": os.path.basename(output_path),
            "timestamp": datetime.now().isoformat(),
            "generation_type": "legacy_animation",
            "prompt_1": prompt_1,
            "negative_prompt": negative_prompt,
            "seed": actual_seed,
            "num_frames": num_frames,
            "inference_steps": animateInferenceSteps,
            "strength": strength,
            "framerate": framerate,
            "prompt_strength": prompt_strength,
            "checkpoint": CURRENT_CHECKPOINT or "base_sdxl",
            "loras": json.loads(loras) if loras != "[]" else [],
            "full_path": output_path
        }
        save_generation_metadata(metadata)

        frames = None
        torch.cuda.empty_cache()
        gc.collect()

        return {"video_path": output_path, "seed_used": actual_seed}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        debug_logger.info("=== Post-legacy animation cleanup ===")
        try:
            if 'pipeline' in locals() and hasattr(pipeline, 'unload_lora_weights'):
                pipeline.unload_lora_weights()
                debug_logger.info("Successfully cleared all LoRA weights")

            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                gc.collect()
                debug_logger.info("CUDA cache cleared")
        except Exception as e:
            debug_logger.error(f"Post-legacy animation cleanup failed: {str(e)}")

def load_lora(pipeline, lora_path, strength=0.5):
    try:
        if not lora_path or lora_path.lower() == "none":
            loaded_logger.info("=== Clearing LoRAs ===")
            if hasattr(pipeline, 'unload_lora_weights'):
                pipeline.unload_lora_weights()
                loaded_logger.info("Successfully unloaded all LoRA weights")
            return pipeline

        sdxl_path = os.path.join("loras_sdxl", lora_path)
        adapter_name = os.path.splitext(os.path.basename(lora_path))[0]
        
        loaded_logger.info("=== Loading LoRA ===")
        loaded_logger.info(f"LoRA file: {lora_path}")
        loaded_logger.info(f"Full path: {sdxl_path}")
        loaded_logger.info(f"Adapter name: {adapter_name}")
        loaded_logger.info(f"Strength: {strength}")
        
        # Check if this adapter is already loaded
        if hasattr(pipeline, 'active_adapters') and adapter_name in pipeline.active_adapters:
            loaded_logger.info(f"LoRA {adapter_name} is already loaded, skipping...")
            return pipeline
            
        # Load LoRA weights with the scale parameter directly
        pipeline.load_lora_weights(
            os.path.dirname(sdxl_path),
            weight_name=os.path.basename(sdxl_path),
            adapter_name=adapter_name
        )
        
        # Set the adapter scale
        pipeline.set_adapters([adapter_name], [strength])
        
        loaded_logger.info(f"Successfully loaded LoRA: {adapter_name}")
        return pipeline
        
    except Exception as e:
        loaded_logger.error(f"Failed to load LoRA: {str(e)}")
        loaded_logger.error(f"Traceback: {traceback.format_exc()}")
        return pipeline

def load_multiple_loras(pipeline, lora_data):
    debug_logger.info("\n=== Starting Multiple LoRA Loading Process ===")
    
    try:
        # Clear existing LoRAs first
        if hasattr(pipeline, 'unload_lora_weights'):
            pipeline.unload_lora_weights()
            debug_logger.info("Cleared existing LoRA weights")
        
        # Load each LoRA
        for lora in lora_data:
            debug_logger.info(f"\nLoading LoRA: {lora['path']}")
            debug_logger.info(f"Weight: {lora['weight']}")
            
            try:
                pipeline = load_lora(pipeline, lora['path'], lora['weight'])
                debug_logger.info(f"Successfully loaded LoRA: {lora['path']}")
            except Exception as e:
                debug_logger.error(f"Failed to load LoRA {lora['path']}: {str(e)}")
                raise
        
        return pipeline
        
    except Exception as e:
        debug_logger.error(f"Error in load_multiple_loras: {str(e)}")
        raise

def warmup_pipeline(pipeline, prompt_strength=...):
    with torch.inference_mode():
        pipeline(
            prompt="dummy",
            num_inference_steps=1,
            guidance_scale=prompt_strength
        )
    torch.cuda.empty_cache()

@bigapp.get("/loras")
async def list_loras():
    sdxl_dir = "loras_sdxl"
    os.makedirs(sdxl_dir, exist_ok=True)
    loras = [f for f in os.listdir(sdxl_dir) if f.endswith(('.safetensors', '.bin'))]
    return {"loras": ["none"] + loras}

@bigapp.post("/reset_pipeline")
async def reset_pipeline():
    try:
        global txt2img_pipe, img2img_pipe, svd_pipe, animatediff_pipe
        loaded_logger.info("=== Pipeline Reset Started ===")

        if txt2img_pipe is not None:
            loaded_logger.info("Unloading Text2Image Pipeline")
            if hasattr(txt2img_pipe, 'unload_lora_weights'):
                loaded_logger.info("Unloading all LoRA weights")
                txt2img_pipe.unload_lora_weights()
            del txt2img_pipe
            txt2img_pipe = None

        if img2img_pipe is not None:
            loaded_logger.info("Unloading Image2Image Pipeline")
            if hasattr(img2img_pipe, 'unload_lora_weights'):
                loaded_logger.info("Unloading all LoRA weights")
                img2img_pipe.unload_lora_weights()
            del img2img_pipe
            img2img_pipe = None

        if SVD_AVAILABLE and svd_pipe is not None:
            loaded_logger.info("Unloading SVD Pipeline")
            del svd_pipe
            svd_pipe = None

        if ANIMATEDIFF_AVAILABLE and animatediff_pipe is not None:
            loaded_logger.info("Unloading AnimateDiff Pipeline")
            if hasattr(animatediff_pipe, 'unload_lora_weights'):
                loaded_logger.info("Unloading all LoRA weights")
                animatediff_pipe.unload_lora_weights()
            del animatediff_pipe
            animatediff_pipe = None

        loaded_logger.info("Pipeline reset completed")
        loaded_logger.info("-------------------")
        loaded_logger.handlers[0].flush()
        
        # Force CUDA cache cleanup
        if torch.cuda.is_available():
            print("[INFO] Clearing CUDA cache...")
            initial_memory = torch.cuda.memory_allocated()/1024**2
            torch.cuda.empty_cache()
            torch.cuda.ipc_collect()
            final_memory = torch.cuda.memory_allocated()/1024**2
            print(f"[INFO] CUDA memory freed: {initial_memory - final_memory:.2f}MB")
        
        # Force garbage collection
        print("[INFO] Running garbage collection...")
        gc.collect()
        
        print("[INFO] Pipeline reset completed successfully")
        return {"status": "success", "message": "Pipeline reset successful"}
        
    except Exception as e:
        error_msg = f"[ERROR] Failed to reset pipeline: {str(e)}"
        print(error_msg)
        print(f"[ERROR] Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to reset pipeline",
                "message": str(e)
            }
        )

# Add checkpoint configuration
CHECKPOINT_DIR = "./checkpoints"  # Directory for your checkpoints
CURRENT_CHECKPOINT = None  # Track currently loaded checkpoint

def load_checkpoint(checkpoint_path: str):
    """Load an SDXL checkpoint and return a pipeline"""
    global CURRENT_CHECKPOINT
    
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")
        
    try:
        # Ensure config path exists and is absolute
        config_path = os.path.abspath("sdxl/configs/sd_xl_base.yaml")
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Config file not found: {config_path}")
            
        print(f"[INFO] Loading checkpoint with config from: {config_path}")
            
        pipeline = StableDiffusionXLPipeline.from_single_file(
            checkpoint_path,
            torch_dtype=torch.float16,
            use_safetensors=(checkpoint_path.endswith('.safetensors')),
            local_files_only=True,
            original_config_file=config_path  # Changed from config_file to original_config_file
        )
        
        if torch.cuda.is_available():
            # Aggressive memory optimization for 6GB cards
            pipeline.enable_model_cpu_offload()
            pipeline.enable_vae_slicing()
            pipeline.enable_attention_slicing(1)  # Most aggressive slicing
            pipeline.enable_vae_tiling()  # Enable VAE tiling for memory efficiency

            try:
                pipeline.enable_xformers_memory_efficient_attention()
            except:
                pass

            # Additional memory optimizations
            try:
                pipeline.unet.set_attn_processor({})  # Use default attention processor
            except:
                pass
        
        CURRENT_CHECKPOINT = checkpoint_path
        return pipeline
    except Exception as e:
        print(f"[ERROR] Failed to load checkpoint: {str(e)}")
        print(f"[ERROR] Traceback: {traceback.format_exc()}")
        raise RuntimeError(f"Failed to load checkpoint: {str(e)}")

# Add endpoint to list and switch checkpoints
@bigapp.get("/checkpoints")
async def list_checkpoints():
    print(f"[DEBUG] Checking for checkpoints in: {CHECKPOINT_DIR}")
    
    if not os.path.exists(CHECKPOINT_DIR):
        print(f"[DEBUG] Checkpoint directory does not exist!")
        return {"checkpoints": []}
    
    # List all files in directory
    all_files = os.listdir(CHECKPOINT_DIR)
    print(f"[DEBUG] All files in directory: {all_files}")
    
    # Filter for checkpoints
    checkpoints = [f for f in all_files 
                  if f.endswith(('.safetensors', '.ckpt'))]
    print(f"[DEBUG] Found checkpoints: {checkpoints}")
    
    return {"checkpoints": checkpoints}

@bigapp.post("/switch_checkpoint")
async def switch_checkpoint(checkpoint_name: str = Form(...)):
    try:
        # Add logging for checkpoint switching
        loaded_logger.info("=== Checkpoint Switch ===")
        loaded_logger.info(f"New Checkpoint: {checkpoint_name}")
        loaded_logger.info("-------------------")
        loaded_logger.handlers[0].flush()  # Force immediate write
        
        global txt2img_pipe, img2img_pipe, svd_pipe, animatediff_pipe, CURRENT_CHECKPOINT

        print(f"\n[INFO] Attempting to switch to checkpoint: {checkpoint_name}")
        print(f"[INFO] Current memory usage: {torch.cuda.memory_allocated()/1024**2:.2f}MB")

        # Clear existing pipelines
        if txt2img_pipe is not None:
            print("[INFO] Unloading text-to-image pipeline...")
            del txt2img_pipe
            txt2img_pipe = None

        if img2img_pipe is not None:
            print("[INFO] Unloading image-to-image pipeline...")
            del img2img_pipe
            img2img_pipe = None

        if SVD_AVAILABLE and svd_pipe is not None:
            print("[INFO] Unloading SVD pipeline...")
            del svd_pipe
            svd_pipe = None

        if ANIMATEDIFF_AVAILABLE and animatediff_pipe is not None:
            print("[INFO] Unloading AnimateDiff pipeline...")
            del animatediff_pipe
            animatediff_pipe = None
        
        if torch.cuda.is_available():
            print("[INFO] Clearing CUDA cache...")
            torch.cuda.empty_cache()
        gc.collect()
        
        if checkpoint_name == "base_sdxl":
            print(f"[INFO] Loading base SDXL model from {model_path}...")
            print(f"[INFO] Checking if path exists: {os.path.exists(model_path)}")
            
            if not os.path.exists(model_path):
                raise FileNotFoundError(f"Base model path not found: {model_path}")
            
            # Create Euler scheduler
            scheduler = EulerDiscreteScheduler(
                beta_start=0.00085,
                beta_end=0.012,
                beta_schedule="scaled_linear",
                timestep_spacing="leading",
                steps_offset=1,
                num_train_timesteps=3201,  # Increased to support up to 3200 steps
                use_karras_sigmas=True
            )
                
            txt2img_pipe = StableDiffusionXLPipeline.from_pretrained(
                model_path,
                torch_dtype=torch.float16,
                scheduler=scheduler,  # Add scheduler here
                local_files_only=True,
                variant="fp16",
                use_safetensors=True,
                text_encoder_2_use_attention_mask=True  # Add this line
            )
            
            if torch.cuda.is_available():
                print("[INFO] Moving model to GPU and enabling optimizations...")
                txt2img_pipe.to("cuda")
                txt2img_pipe.enable_model_cpu_offload()
                txt2img_pipe.enable_vae_slicing()
                txt2img_pipe.enable_attention_slicing()
                try:
                    txt2img_pipe.enable_xformers_memory_efficient_attention()
                except Exception as xform_error:
                    print(f"[WARNING] Couldn't enable xformers: {str(xform_error)}")
                    
            CURRENT_CHECKPOINT = None
            message = "Successfully switched to base SDXL model"
            
        else:
            print(f"[INFO] Loading checkpoint: {checkpoint_name}")
            checkpoint_path = os.path.join(CHECKPOINT_DIR, checkpoint_name)
            print(f"[INFO] Full checkpoint path: {checkpoint_path}")
            print(f"[INFO] Checking if path exists: {os.path.exists(checkpoint_path)}")
            
            if not os.path.exists(checkpoint_path):
                raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")
                
            txt2img_pipe = load_checkpoint(checkpoint_path)
            CURRENT_CHECKPOINT = checkpoint_path
            message = f"Successfully switched to checkpoint: {checkpoint_name}"
            
        print(f"[INFO] {message}")
        print(f"[INFO] Final memory usage: {torch.cuda.memory_allocated()/1024**2:.2f}MB")
        return {"message": message}
        
    except Exception as e:
        error_msg = f"Failed to switch checkpoint: {str(e)}"
        print(f"[ERROR] {error_msg}")
        print(f"[ERROR] Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail={"error": error_msg}
        )

@bigapp.get("/check_loaded_state")
async def check_loaded_state():
    global txt2img_pipe, CURRENT_CHECKPOINT
    try:
        loaded_logger.info("\n=== Current System State Check ===")
        loaded_logger.info(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Check if pipeline exists
        if txt2img_pipe is None:
            loaded_logger.info("Status: No pipeline currently loaded")
            return {"message": "No pipeline currently loaded"}
            
        # Check current checkpoint
        if CURRENT_CHECKPOINT:
            loaded_logger.info(f"Current Checkpoint: {os.path.basename(CURRENT_CHECKPOINT)}")
        else:
            loaded_logger.info("Current Model: Base SDXL")
            
        # Check active LoRAs
        active_loras = []
        if hasattr(txt2img_pipe, 'active_adapters'):
            active_loras = txt2img_pipe.active_adapters
            
        if active_loras:
            loaded_logger.info("\nActive LoRAs:")
            for lora in active_loras:
                loaded_logger.info(f"- {lora}")
        else:
            loaded_logger.info("\nNo active LoRAs")
            
        # Check VRAM usage if on CUDA
        if torch.cuda.is_available():
            vram_used = torch.cuda.memory_allocated()/1024**2
            vram_total = torch.cuda.get_device_properties(0).total_memory/1024**2
            loaded_logger.info(f"\nVRAM Usage: {vram_used:.2f}MB / {vram_total:.2f}MB")
            
        loaded_logger.info("\n-------------------")
        loaded_logger.handlers[0].flush()
        
        return {"message": "Current state has been written to loaded.txt"}
        
    except Exception as e:
        error_msg = f"Error checking loaded state: {str(e)}"
        loaded_logger.error(error_msg)
        loaded_logger.info("-------------------")
        loaded_logger.handlers[0].flush()
        raise HTTPException(status_code=500, detail=error_msg)





















































