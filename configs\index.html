
<!DOCTYPE html>
<html>
<head>
    <title>SDXL Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .control-group {
            margin-bottom: 15px;
        }
        .control-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        textarea, input[type="text"], input[type="number"], select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #resetButton {
            background-color: #f44336;
        }
        #resetButton:hover {
            background-color: #da190b;
        }
        .progress {
            height: 20px;
            background-color: #f5f5f5;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        #output {
            margin-top: 20px;
            text-align: center;
        }
        #output img {
            max-width: 100%;
            height: auto;
        }
        .lora-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        #debugInfo {
            font-family: monospace;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .batch-item {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .batch-item p {
            margin: 0 0 10px 0;
            font-weight: bold;
        }
        .batch-item img, .batch-item video {
            max-width: 100%;
            height: auto;
        }
        #batchProgress {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 4px;
        }
        .checkpoint-container {
            margin: 10px 0;
        }
        .status-message {
            margin-top: 5px;
            padding: 5px;
            font-weight: bold;
        }
        .model-indicator {
            margin-top: 5px;
            font-style: italic;
            color: #666;
        }
        #loading_indicator {
            color: blue;
            font-weight: bold;
            margin-top: 5px;
        }
        #promptLog div {
            border-bottom: 1px solid #eee;
            padding: 5px 0;
        }

        #promptLog div:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SDXL Interface</h1>

        <!-- Reset Pipeline Button -->
        <button id="resetButton" onclick="resetPipeline()">Reset Pipeline</button>

        <!-- Main Controls -->
        <div class="control-group">
            <h3>Prompt</h3>
            <div style="display: flex; gap: 20px;">
                <!-- Left side: Prompt controls -->
                <div style="flex: 1;">
                    <div class="input-group">
                        <label for="prompt_1">Primary Prompt:</label>
                        <textarea id="prompt_1" oninput="updateTokenCount()" rows="4" style="width: 100%" placeholder="Enter your primary prompt here"></textarea>
                        <div id="token_count_1">Tokens: 0</div>
                    </div>

                    <div class="input-group">
                        <label for="prompt_2">Secondary Prompt:</label>
                        <textarea id="prompt_2" oninput="updateTokenCount()" rows="4" style="width: 100%" placeholder="Enter your secondary prompt here"></textarea>
                        <div id="token_count_2">Tokens: 0</div>
                    </div>

                    <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; margin: 10px 0;">
                        <strong>Example Prompts (Available Keys: AGE, RACE, SEX, SHOT, HAIR, BODY, EMO, CLOTHS, CUS1 - CUS1000):</strong>
                        <ul style="margin: 5px 0; padding-left: 20px;">
                            <li>"SHOT of a RACE SEX with HAIR hair who is AGE, BODY build, CUS1 and CUS2"</li>
                            <li>"SHOT photograph of AGE RACE SEX person with HAIR hair and BODY physique, CUS1, CUS2, CUS3"</li>
                            <li>"SHOT style portrait of BODY RACE SEX with HAIR hair, age AGE, CUS1 CUS2 CUS3 CUS4"</li>
                            <li>"AGE RACE SEX person with HAIR hair and BODY build, SHOT photograph, looking CUS1 and CUS2"</li>
                        </ul>
                    </div>

                    <h3>Negative Prompt</h3>
                    <textarea id="negative_prompt" rows="2" placeholder="Enter negative prompt here"></textarea>
                </div>
            </div>

            <div class="control-row">
                <div>
                    <label>Width:</label>
                    <input type="number" id="width" value="1024" min="512" max="2048" step="8">
                </div>
                <div>
                    <label>Height:</label>
                    <input type="number" id="height" value="1024" min="512" max="2048" step="8">
                </div>
                <div>
                    <label>Steps:</label>
                    <input type="number" id="steps" value="30" min="1" max="3200">
                </div>
                <div>
                    <label>Prompt Strength:</label>
                    <input type="number" id="prompt_strength" value="7.5" min="0.1" max="100.0" step="0.1">
                </div>
                <div>
                    <label>Seed:</label>
                    <input type="number" id="seed" value="-1" min="-1" max="4294967295" style="width: 120px;">
                    <button type="button" onclick="randomizeSeed()" title="Generate random seed" style="margin-left: 5px;">🎲</button>
                    <small style="color: #666; display: block; font-size: 0.8em;">-1 = random, 0-4294967295 = fixed seed</small>
                </div>
            </div>
        </div>

        <!-- LoRA Controls -->
        <div class="control-group">
            <h3>LoRA Selection</h3>
            <select id="lora"></select>
            <input type="number" id="lora_strength" value="..." min="0.1" max="2" step="0.1">
            <button onclick="addLora()">Add LoRA</button>
            <div id="selected_loras"></div>
        </div>

        <!-- Checkpoint Controls -->
        <div class="control-group">
            <h3>Checkpoint Selection</h3>
            <div class="checkpoint-container">
                <select id="checkpoint" class="form-control">
                    <option value="base_sdxl">Base SDXL Model</option>
                </select>
                <button onclick="checkLoadedState()" class="btn btn-info">Check Loaded State</button>
                <div id="checkpoint_status" class="status-message"></div>
                <div id="loading_indicator" style="display: none;">Loading...</div>
                <div id="model_indicator" class="model-indicator">Base SDXL</div>
            </div>
            <button onclick="switchCheckpoint()">Switch Checkpoint</button>
        </div>

        <!-- Generation Controls -->
        <div class="control-group">
            <h3>Generation Type</h3>
            <div class="form-group">
                <label for="generation_type">Generation Type:</label>
                <select id="generation_type" class="form-control">
                    <option value="txt2img">Text to Image</option>
                    <option value="img2img">Image to Image</option>
                    <option value="animate">Animation</option>
                </select>
            </div>

            <div id="img2img_controls" style="display: none;">
                <div class="input-group mb-3">
                    <label class="input-group-text" for="templateImage">Template Image:</label>
                    <input type="file" class="form-control" id="templateImage" accept="image/*">
                </div>
                <div class="input-group mb-3">
                    <label class="input-group-text" for="templateStrength">Template Strength:</label>
                    <input type="number" class="form-control" id="templateStrength" min="0.1" max="20.0" step="0.1" value="0.7" style="width: 80px;">
                </div>
            </div>

            <div id="animate_controls" style="display: none;">
                <!-- Animation Type Selector -->
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                    <h4 style="margin-top: 0;">🎬 Animation Type</h4>
                    <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="radio" name="animation_type" value="svd" checked style="margin-right: 8px;">
                            <strong>SVD (Image-to-Video)</strong> - High quality, no prompts needed
                        </label>
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="radio" name="animation_type" value="legacy" style="margin-right: 8px;">
                            <strong>Legacy (img2img)</strong> - Old method, not recommended
                        </label>
                    </div>
                </div>

                <!-- SVD Controls -->
                <div id="svd_controls" style="display: block;">
                    <div style="background: #e8f4fd; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <h4 style="margin-top: 0; color: #0066cc;">🎬 Stable Video Diffusion (SVD)</h4>
                        <p style="margin-bottom: 0; font-size: 0.9em; color: #555;">
                            High-quality video generation using Stability AI's official video model.
                            No prompts needed - SVD creates motion based on the input image content.
                        </p>
                    </div>

                    <div class="input-group mb-3">
                        <label class="input-group-text" for="svd_image">Source Image:</label>
                        <input type="file" class="form-control" id="svd_image" accept="image/*">
                    </div>

                    <div class="input-group mb-3">
                        <label class="input-group-text" for="svd_frames">Number of Frames:</label>
                        <input type="number" class="form-control" id="svd_frames" value="25" min="14" max="25">
                        <small style="color: #666; margin-left: 10px;">SVD supports 14-25 frames</small>
                    </div>

                    <div class="input-group mb-3">
                        <label class="input-group-text" for="motion_bucket_id">Motion Intensity:</label>
                        <input type="number" class="form-control" id="motion_bucket_id" value="127" min="1" max="255">
                        <small style="color: #666; margin-left: 10px;">1=minimal motion, 255=maximum motion</small>
                    </div>

                    <div class="input-group mb-3">
                        <label class="input-group-text" for="svd_fps">Frame Rate (FPS):</label>
                        <input type="number" class="form-control" id="svd_fps" value="7" min="5" max="30">
                        <small style="color: #666; margin-left: 10px;">Recommended: 6-10 FPS</small>
                    </div>

                    <div class="input-group mb-3">
                        <label class="input-group-text" for="decode_chunk_size">Decode Chunk Size:</label>
                        <input type="number" class="form-control" id="decode_chunk_size" value="8" min="1" max="16">
                        <small style="color: #666; margin-left: 10px;">Lower = less VRAM, higher = faster</small>
                    </div>
                </div>

                <!-- AnimateDiff removed - not needed for image generation -->

                <!-- Legacy Controls -->
                <div id="legacy_controls" style="display: none;">
                    <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <h4 style="margin-top: 0; color: #856404;">⚠️ Legacy Animation (Not Recommended)</h4>
                        <p style="margin-bottom: 0; font-size: 0.9em; color: #555;">
                            Old iterative img2img method. Creates fuzzy results and accumulates artifacts.
                            Use SVD or AnimateDiff instead for better quality.
                        </p>
                    </div>

                    <div class="input-group mb-3">
                        <label class="input-group-text" for="legacy_image">Source Image:</label>
                        <input type="file" class="form-control" id="legacy_image" accept="image/*">
                    </div>

                    <div class="input-group mb-3">
                        <label class="input-group-text" for="legacy_frames">Number of Frames:</label>
                        <input type="number" class="form-control" id="legacy_frames" value="30" min="1" max="100">
                    </div>

                    <div class="input-group mb-3">
                        <label class="input-group-text" for="animateStrength">Animation Strength:</label>
                        <input type="number" class="form-control" id="animateStrength" min="0.01" max="1.0" step="0.05" value="0.3">
                    </div>

                    <div class="input-group mb-3">
                        <label class="input-group-text" for="animateSteps">Animation Steps:</label>
                        <input type="number" class="form-control" id="animateSteps" value="25" min="1" max="100">
                    </div>

                    <div class="input-group mb-3">
                        <label class="input-group-text" for="legacy_framerate">Frame Rate:</label>
                        <input type="number" class="form-control" id="legacy_framerate" value="30" min="1" max="120">
                    </div>
                </div>

                <!-- Tips Section -->
                <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 15px;">
                    <strong>💡 Tips:</strong>
                    <ul style="margin: 5px 0; padding-left: 20px; font-size: 0.9em;">
                        <li><strong>SVD:</strong> Best for realistic motion from photos. No prompts needed. Uses any image.</li>
                        <li><strong>Legacy:</strong> Avoid unless you need specific compatibility. Creates fuzzy results.</li>
                        <li>First SVD generation may take longer as the model downloads (~7GB).</li>
                        <li><strong>SVD works best with:</strong> Clear, well-lit photos with obvious motion potential.</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Batch Controls -->
        <div class="control-group">
            <h3>Batch Processing</h3>
            <div class="control-row" style="display: flex; justify-content: space-between; align-items: start;">
                <div>
                    <div>
                        <label><input type="checkbox" id="batchEnabled"> Enable Batch</label>
                    </div>
                    <div>
                        <label>Number of Images:</label>
                        <input type="number" id="batchCount" value="1" min="1" max="100">
                    </div>
                    <div style="margin-top: 10px;">
                        <label><input type="checkbox" id="stepIncreaseEnabled"> Auto Step Increase</label>
                    </div>
                    <div>
                        <label>Step Increase per Batch:</label>
                        <input type="number" id="stepIncrease" value="5" min="1" max="100">
                        <small style="color: #666; display: block; font-size: 0.8em;">Each batch will increase steps by this amount</small>
                    </div>
                    <div style="margin-top: 10px;">
                        <label><input type="checkbox" id="strengthIncreaseEnabled"> Auto Strength Increase</label>
                    </div>
                    <div>
                        <label>Strength Increase per Batch:</label>
                        <input type="number" id="strengthIncrease" value="0.5" min="0.1" max="5.0" step="0.1">
                        <small style="color: #666; display: block; font-size: 0.8em;">Each batch will increase prompt strength by this amount</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Randomization Controls -->
        <div class="control-group">
            <h3>Randomization Controls</h3>

            <!-- Age List Controls -->
            <div class="control-row">
                <div>
                    <label>Enter Ages (one per line):</label>
                    <textarea id="ageList" rows="5" style="width: 100%; margin-top: 5px;"
                        placeholder="Enter each age on a new line, for example:
infant
33 year old
32 year old
approx 12 years old
elderly
teen
young adult"></textarea>
                    <button onclick="updateAgeList()" style="margin-top: 5px;">Update Ages</button>
                </div>
            </div>

            <div style="display: flex; gap: 20px;">
                <!-- Left side: Race and Sex Controls -->
                <div style="flex: 1;">
                    <!-- Race Controls -->
                    <div class="control-row">
                        <div>
                            <label>Enter Races (one per line):</label>
                            <textarea id="raceList" rows="5" style="width: 100%; margin-top: 5px;"
                                placeholder="Enter each race on a new line, for example:
Japanese
Chinese
Thai
Russian
Korean"></textarea>
                            <button onclick="updateRaceList()" style="margin-top: 5px;">Update Races</button>
                        </div>
                    </div>

                    <!-- Sex Controls -->
                    <div class="control-row">
                        <div>
                            <label>Enter Sexes (one per line):</label>
                            <textarea id="sexList" rows="2" style="width: 100%; margin-top: 5px;"
                                placeholder="Enter each sex on a new line, for example:
male
female"></textarea>
                            <button onclick="updateSexList()" style="margin-top: 5px;">Update Sexes</button>
                        </div>
                    </div>

                    <!-- Clothing Controls -->
                    <div class="control-row">
                        <div>
                            <label>Clothing Options:</label>
                            <textarea id="clothingList" rows="5" style="width: 100%; margin-top: 5px;"
                                placeholder="Enter each clothing option on a new line, for example:
wearing a suit
in casual attire
in business casual
in traditional dress
in formal wear"></textarea>
                            <button onclick="updateClothing()" style="margin-top: 5px;">Update Clothing</button>
                        </div>
                    </div>
                </div>

                <!-- Right side: Generation Log -->
                <div style="width: 400px;">
                    <h3>Generation Log</h3>
                    <div id="promptLog" style="height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; margin-bottom: 10px; background-color: #fff;"></div>
                    <button id="generateButton" style="width: 100%; margin-top: 10px; padding: 10px; font-size: 16px;" onclick="generate()">Generate</button>
                    <div style="display: flex; gap: 10px; margin-top: 10px;">
                        <button onclick="resetPipeline()" style="flex: 1; padding: 8px; font-size: 14px;">Reset Pipeline</button>
                        <button onclick="viewGenerationHistory()" style="flex: 1; padding: 8px; font-size: 14px;">View History</button>
                    </div>
                </div>
            </div>

            <!-- Shot Types & Camera Angles Controls -->
            <div class="control-row">
                <div>
                    <label>Enter Shot Types (one per line):</label>
                    <textarea id="shotTypeList" rows="5" style="width: 100%; margin-top: 5px;"
                        placeholder="Enter each shot type on a new line, for example:
close-up shot
medium shot
full body shot
from below
side view
portrait style"></textarea>
                    <button onclick="updateShotTypes()" style="margin-top: 5px;">Update Shot Types</button>
                </div>
            </div>

            <!-- Hair Colors & Body Types Controls -->
            <div class="control-row">
                <div>
                    <label>Hair Colors:</label>
                    <textarea id="hairColorList" rows="5" style="width: 100%; margin-top: 5px;"
                        placeholder="Enter each hair color on a new line, for example:
black
brown
blonde
red
gray
white"></textarea>
                    <button onclick="updateHairColors()" style="margin-top: 5px;">Update Hair Colors</button>
                </div>

                <div>
                    <label>Body Types:</label>
                    <textarea id="bodyTypeList" rows="5" style="width: 100%; margin-top: 5px;"
                        placeholder="Enter each body type on a new line, for example:
athletic
slim
average
muscular
curvy"></textarea>
                    <button onclick="updateBodyTypes()" style="margin-top: 5px;">Update Body Types</button>
                </div>
            </div>

            <!-- Emotions Controls -->
            <div class="control-row">
                <div>
                    <label>Emotions:</label>
                    <textarea id="emotionList" rows="5" style="width: 100%; margin-top: 5px; resize: none;"
                        placeholder="Enter each emotion on a new line, for example:
happy
sad
angry
neutral
confident
thoughtful"></textarea>
                    <button onclick="updateEmotions()" style="margin-top: 5px;">Update Emotions</button>
                </div>
            </div>

            <!-- Dynamic Custom Descriptors Controls -->
            <div class="control-row">
                <div style="margin-bottom: 15px; padding: 10px; background: #f0f0f0; border-radius: 4px;">
                    <h4>Dynamic Custom Descriptors</h4>
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <label style="margin-right: 10px;">Number of Custom Descriptors:</label>
                        <input type="number" id="customDescriptorCount" min="1" max="1000" value="8" style="width: 80px;">
                        <button onclick="generateCustomDescriptorFields()" style="margin-left: 10px;">Apply</button>
                    </div>
                    <p style="font-size: 0.9em; color: #666;">You can create up to 1000 custom descriptor fields. Use CUS1, CUS2, etc. in your prompts to reference them.</p>
                </div>
            </div>

            <!-- Container for dynamically generated custom descriptor fields -->
            <div id="customDescriptorsContainer" class="control-row" style="display: flex; flex-wrap: wrap; gap: 15px; justify-content: flex-start;">
                <!-- Fields will be generated here by JavaScript -->
            </div>

            <!-- Display current options -->
            <div id="randomizationInfo" class="info-box" style="margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
                <h4>Current Settings:</h4>

                <div><b>Selected Ages:</b></div>
                <div style="margin-bottom: 15px;"><span id="ageRangeDisplay"></span></div>

                <div><b>Selected Races:</b></div>
                <div style="margin-bottom: 15px;"><span id="racesDisplay"></span></div>

                <div><b>Selected Sexes:</b></div>
                <div style="margin-bottom: 15px;"><span id="sexesDisplay"></span></div>

                <div><b>Selected Clothing:</b></div>
                <div style="margin-bottom: 15px;"><span id="clothingDisplay"></span></div>

                <div><b>Selected Shot Types:</b></div>
                <div style="margin-bottom: 15px;"><span id="shotTypesDisplay"></span></div>

                <div><b>Selected Hair Colors:</b></div>
                <div style="margin-bottom: 15px;"><span id="hairColorsDisplay"></span></div>

                <div><b>Selected Body Types:</b></div>
                <div style="margin-bottom: 15px;"><span id="bodyTypesDisplay"></span></div>

                <div><b>Selected Emotions:</b></div>
                <div style="margin-bottom: 15px;"><span id="emotionsDisplay"></span></div>

                <div><b>Selected Custom Descriptors:</b></div>
                <div style="margin-bottom: 15px;"><span id="customDescriptorsDisplay"></span></div>
            </div>
        </div>

        <!-- Debug Info -->
        <div id="debugInfo"></div>

        <!-- Output Display removed to prevent file locking -->
    </div>

    <script>

  // Add these variables at the top of your script section, before any functions
        let currentRaces = [];
        let currentSexes = [];
        let currentAges = [];
        let currentShotTypes = [];
        let currentHairColors = [];
        let currentBodyTypes = [];
        let currentEmotions = [];
        let currentClothing = [];

        // Dynamic custom descriptors array
        let customDescriptors = [];
        let maxCustomDescriptors = 1000; // Maximum number of custom descriptors

        // Load available LoRAs on page load
        document.addEventListener('DOMContentLoaded', () => {
            loadLoras();
            updateTokenCount();
            generateCustomDescriptorFields(); // Initialize custom descriptor fields
        });

        // Token counting
        document.addEventListener('DOMContentLoaded', () => {
            // Add event listeners for both prompt fields
            document.getElementById('prompt_1').addEventListener('input', updateTokenCount);
            document.getElementById('prompt_2').addEventListener('input', updateTokenCount);

            // Your existing DOMContentLoaded code
            loadLoras();
            updateTokenCount();

            // Load saved custom descriptor count if available
            const savedData = localStorage.getItem('generationSettings');
            if (savedData) {
                const data = JSON.parse(savedData);
                if (data.customDescriptorCount) {
                    document.getElementById('customDescriptorCount').value = data.customDescriptorCount;
                }
            }

            // Initialize custom descriptors
            generateCustomDescriptorFields();
        });

        function updateTokenCount() {
            // Update token count for primary prompt
            const prompt1 = document.getElementById('prompt_1').value;
            const tokenCount1 = prompt1.split(/\s+/).filter(word => word.length > 0).length;
            document.getElementById('token_count_1').textContent = `Tokens: ${tokenCount1}`;

            // Update token count for secondary prompt
            const prompt2 = document.getElementById('prompt_2').value;
            const tokenCount2 = prompt2.split(/\s+/).filter(word => word.length > 0).length;
            document.getElementById('token_count_2').textContent = `Tokens: ${tokenCount2}`;
        }

        // Generation type switching
        document.getElementById('generation_type').addEventListener('change', function() {
            const img2imgControls = document.getElementById('img2img_controls');
            const animateControls = document.getElementById('animate_controls');

            img2imgControls.style.display = this.value === 'img2img' ? 'block' : 'none';
            animateControls.style.display = this.value === 'animate' ? 'block' : 'none';
        });

        // Animation type switching
        document.querySelectorAll('input[name="animation_type"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const svdControls = document.getElementById('svd_controls');
                const legacyControls = document.getElementById('legacy_controls');

                // Hide all controls first
                svdControls.style.display = 'none';
                legacyControls.style.display = 'none';

                // Show the selected control
                if (this.value === 'svd') {
                    svdControls.style.display = 'block';
                } else if (this.value === 'legacy') {
                    legacyControls.style.display = 'block';
                }
            });
        });

        // Seed management functions
        function randomizeSeed() {
            const randomSeed = Math.floor(Math.random() * 4294967295);
            document.getElementById('seed').value = randomSeed;
        }

        function getSeedValue() {
            const seedInput = document.getElementById('seed').value;
            const seed = parseInt(seedInput);
            return (seed === -1) ? Math.floor(Math.random() * 4294967295) : seed;
        }

        // Generation history functions
        async function viewGenerationHistory() {
            try {
                console.log('Fetching generations.json...');
                const response = await fetch('/generations.json');
                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);

                if (response.ok) {
                    const generations = await response.json();
                    console.log('Generations loaded:', generations.length, 'items');
                    console.log('First generation:', generations[0]);

                    if (generations && generations.length > 0) {
                        // Sort most recent first and display all
                        const sortedGenerations = generations.reverse();
                        displayGenerationHistory(sortedGenerations);
                    } else {
                        console.log('Generations array is empty or null');
                        alert('No generation history found.');
                    }
                } else {
                    console.log('Response not ok, status:', response.status);
                    alert('No generation history found.');
                }
            } catch (error) {
                console.error('Error fetching generation history:', error);
                alert('Error fetching generation history: ' + error.message);
            }
        }

        function displayGenerationHistory(generations) {
            // Create a modal-like display
            const historyHtml = `
                <div id="historyModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; display: flex; justify-content: center; align-items: center;">
                    <div style="background: white; padding: 20px; border-radius: 10px; max-width: 95%; max-height: 95%; overflow-y: auto;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h2>Generation History</h2>
                            <button onclick="closeHistoryModal()" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer;">✕</button>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <input type="text" id="historySearch" placeholder="Search prompts..." style="width: 300px; padding: 5px; margin-right: 10px;">
                            <button onclick="searchHistory()" style="padding: 5px 10px;">Search</button>
                            <button onclick="viewGenerationHistory()" style="padding: 5px 10px; margin-left: 5px;">Show All</button>
                        </div>
                        <div id="historyContent">
                            ${generateHistoryTable(generations)}
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', historyHtml);
        }

        function generateHistoryTable(generations) {
            let html = `<div style="margin-bottom: 10px; color: #666; font-weight: bold;">Showing all ${generations.length} generations</div>`;
            html += '<table style="width: 100%; border-collapse: collapse; font-size: 12px;">';
            html += '<tr style="background: #f8f9fa; font-weight: bold;">';
            html += '<th style="border: 1px solid #ddd; padding: 8px;">Filename</th>';
            html += '<th style="border: 1px solid #ddd; padding: 8px;">Type</th>';
            html += '<th style="border: 1px solid #ddd; padding: 8px;">Prompt 1</th>';
            html += '<th style="border: 1px solid #ddd; padding: 8px;">Seed</th>';
            html += '<th style="border: 1px solid #ddd; padding: 8px;">Settings</th>';
            html += '<th style="border: 1px solid #ddd; padding: 8px;">Actions</th>';
            html += '</tr>';

            // Display ALL generations without any limit
            generations.forEach(gen => {
                html += '<tr>';
                html += `<td style="border: 1px solid #ddd; padding: 4px; font-family: monospace; font-size: 10px;">${gen.filename}</td>`;
                html += `<td style="border: 1px solid #ddd; padding: 4px;">${gen.generation_type}</td>`;
                html += `<td style="border: 1px solid #ddd; padding: 4px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title="${gen.prompt_1 || ''}">${(gen.prompt_1 || '').substring(0, 50)}${(gen.prompt_1 || '').length > 50 ? '...' : ''}</td>`;
                html += `<td style="border: 1px solid #ddd; padding: 4px;">${gen.seed}</td>`;

                let settings = '';
                if (gen.width && gen.height) settings += `${gen.width}x${gen.height} `;
                if (gen.inference_steps) settings += `${gen.inference_steps}s `;
                if (gen.prompt_strength) settings += `${gen.prompt_strength}cfg`;
                html += `<td style="border: 1px solid #ddd; padding: 4px;">${settings}</td>`;

                html += `<td style="border: 1px solid #ddd; padding: 4px;">`;
                html += `<button onclick="loadGenerationSettings(${JSON.stringify(gen).replace(/"/g, '&quot;')})" style="padding: 2px 6px; font-size: 10px; margin-right: 5px;">Load</button>`;
                html += `<button onclick="copyGenerationInfo(${JSON.stringify(gen).replace(/"/g, '&quot;')})" style="padding: 2px 6px; font-size: 10px;">Copy</button>`;
                html += `</td>`;
                html += '</tr>';
            });

            html += '</table>';
            return html;
        }

        function closeHistoryModal() {
            const modal = document.getElementById('historyModal');
            if (modal) {
                modal.remove();
            }
        }

        async function searchHistory() {
            const searchTerm = document.getElementById('historySearch').value;
            try {
                const response = await fetch('/generations.json');
                if (response.ok) {
                    const allGenerations = await response.json();

                    let filteredGenerations;
                    if (!searchTerm.trim()) {
                        filteredGenerations = allGenerations.reverse();
                    } else {
                        // Simple search across all fields
                        filteredGenerations = allGenerations.filter(gen => {
                            const searchableText = JSON.stringify(gen).toLowerCase();
                            return searchableText.includes(searchTerm.toLowerCase());
                        }).reverse();
                    }

                    const content = document.getElementById('historyContent');
                    content.innerHTML = generateHistoryTable(filteredGenerations);
                }
            } catch (error) {
                console.error('Error searching history:', error);
            }
        }

        function loadGenerationSettings(generation) {
            // Load settings from a previous generation
            if (generation.prompt_1) document.getElementById('prompt_1').value = generation.prompt_1;
            if (generation.prompt_2) document.getElementById('prompt_2').value = generation.prompt_2;
            if (generation.negative_prompt) document.getElementById('negative_prompt').value = generation.negative_prompt;
            if (generation.seed) document.getElementById('seed').value = generation.seed;
            if (generation.width) document.getElementById('width').value = generation.width;
            if (generation.height) document.getElementById('height').value = generation.height;
            if (generation.inference_steps) document.getElementById('steps').value = generation.inference_steps;
            if (generation.prompt_strength) document.getElementById('prompt_strength').value = generation.prompt_strength;

            closeHistoryModal();
            alert('Settings loaded! You can now generate with the same parameters.');
        }

        function copyGenerationInfo(generation) {
            const info = `Filename: ${generation.filename}
Prompt 1: ${generation.prompt_1 || ''}
Prompt 2: ${generation.prompt_2 || ''}
Negative: ${generation.negative_prompt || ''}
Seed: ${generation.seed}
Size: ${generation.width}x${generation.height}
Steps: ${generation.inference_steps}
CFG: ${generation.prompt_strength}
Checkpoint: ${generation.checkpoint}`;

            navigator.clipboard.writeText(info).then(() => {
                alert('Generation info copied to clipboard!');
            }).catch(() => {
                alert('Failed to copy to clipboard');
            });
        }

        // Template strength is now a number input, no need for a range event listener

        // LoRA management
        let selectedLoras = [];

        async function loadLoras() {
            try {
                // Add debug log
                console.log('Fetching LoRAs...');

                const response = await fetch('/loras');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Received LoRAs data:', data); // Debug log

                const loraSelect = document.getElementById('lora');
                if (!loraSelect) {
                    throw new Error('LoRA select element not found');
                }

                // Add a default "none" option
                loraSelect.innerHTML = '<option value="none">None</option>';

                // Add the LoRAs if they exist
                if (data.loras && Array.isArray(data.loras)) {
                    const options = data.loras.map(lora =>
                        `<option value="${lora}">${lora}</option>`
                    );
                    loraSelect.innerHTML += options.join('');
                } else {
                    console.warn('No LoRAs found in response');
                }

                // Set default value for lora_strength
                const strengthInput = document.getElementById('lora_strength');
                if (strengthInput) {
                    strengthInput.value = "1.0";
                }

            } catch (error) {
                console.error('Failed to load LoRAs:', error);
                // Display error in UI
                const loraSelect = document.getElementById('lora');
                if (loraSelect) {
                    loraSelect.innerHTML = '<option value="none">Error loading LoRAs</option>';
                }
                // Show error in debug info
                const debugInfo = document.getElementById('debugInfo');
                if (debugInfo) {
                    debugInfo.textContent = `Failed to load LoRAs: ${error.message}`;
                }
            }
        }

        function addLora() {
            const lora = document.getElementById('lora').value;
            const strength = parseFloat(document.getElementById('lora_strength').value);

            if (lora && lora !== 'none') {
                // Match the backend's expected format exactly
                selectedLoras.push({
                    path: lora,
                    weight: Math.min(Math.max(strength, 0), 2.0) // Clamp between 0 and 2.0
                });
                updateLoraDisplay();
                console.log('Current LoRAs:', selectedLoras); // Debug log
            }
        }

        function removeLora(index) {
            selectedLoras.splice(index, 1);
            updateLoraDisplay();
            console.log('Updated LoRAs after removal:', selectedLoras); // Debug log
        }

        function updateLoraDisplay() {
            const container = document.getElementById('selected_loras');
            container.innerHTML = selectedLoras.map((lora, index) => `
                <div class="lora-item">
                    <span>${lora.path} (${lora.weight})</span>
                    <button onclick="removeLora(${index})">Remove</button>
                </div>
            `).join('');
        }

        // Pipeline reset
        async function resetPipeline() {
            try {
                const response = await fetch('/reset_pipeline', { method: 'POST' });
                const data = await response.json();
                document.getElementById('debugInfo').textContent = data.message;
                // Reload LoRAs after pipeline reset
                await loadLoras();
            } catch (error) {
                document.getElementById('debugInfo').textContent = `Reset failed: ${error}`;
            }
        }

        // Progress tracking
        let progressEventSource;

        function startProgressTracking() {
            // Close any existing connection
            if (progressEventSource) {
                progressEventSource.close();
            }

            // Connect to the SSE endpoint
            progressEventSource = new EventSource('/generation_progress');

            progressEventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                const progress = data.progress * 100; // Convert to percentage
                console.log('Progress update received:', progress); // Debug log
                const progressBar = document.getElementById('progressBar');
                if (progressBar) {
                    progressBar.style.width = `${progress}%`;
                    progressBar.setAttribute('aria-valuenow', progress);
                }
            };

            progressEventSource.onerror = function(error) {
                console.error('SSE Error:', error);
                progressEventSource.close();
            };
        }

        function stopProgressTracking() {
            if (progressEventSource) {
                progressEventSource.close();
                progressEventSource = null;
            }
            // Reset progress bar
            const progressBar = document.getElementById('progressBar');
            if (progressBar) {
                progressBar.style.width = '0%';
                progressBar.setAttribute('aria-valuenow', 0);
            }
        }

        // Batch processing
        document.getElementById('batchEnabled').addEventListener('change', function() {
            const batchControls = document.getElementById('batchCount');
            const stepIncreaseCheckbox = document.getElementById('stepIncreaseEnabled');
            const strengthIncreaseCheckbox = document.getElementById('strengthIncreaseEnabled');

            batchControls.disabled = !this.checked;

            // Disable increase checkboxes if batch is disabled
            if (!this.checked) {
                stepIncreaseCheckbox.checked = false;
                stepIncreaseCheckbox.disabled = true;
                strengthIncreaseCheckbox.checked = false;
                strengthIncreaseCheckbox.disabled = true;
            } else {
                stepIncreaseCheckbox.disabled = false;
                strengthIncreaseCheckbox.disabled = false;
            }

            // Update input states
            updateStepIncreaseState();
            updateStrengthIncreaseState();
        });

        // Step increase processing
        document.getElementById('stepIncreaseEnabled').addEventListener('change', function() {
            updateStepIncreaseState();
        });

        // Strength increase processing
        document.getElementById('strengthIncreaseEnabled').addEventListener('change', function() {
            updateStrengthIncreaseState();
        });

        // Function to update step increase input state
        function updateStepIncreaseState() {
            const batchEnabled = document.getElementById('batchEnabled').checked;
            const stepIncreaseEnabled = document.getElementById('stepIncreaseEnabled').checked;
            const stepIncreaseInput = document.getElementById('stepIncrease');

            // Enable step increase input only if both batch and step increase are enabled
            stepIncreaseInput.disabled = !batchEnabled || !stepIncreaseEnabled;
        }

        // Function to update strength increase input state
        function updateStrengthIncreaseState() {
            const batchEnabled = document.getElementById('batchEnabled').checked;
            const strengthIncreaseEnabled = document.getElementById('strengthIncreaseEnabled').checked;
            const strengthIncreaseInput = document.getElementById('strengthIncrease');

            // Enable strength increase input only if both batch and strength increase are enabled
            strengthIncreaseInput.disabled = !batchEnabled || !strengthIncreaseEnabled;
        }

        // Local Storage functions to persist the lists
        function saveToLocalStorage() {
            const dataToSave = {
                prompt_1: document.getElementById('prompt_1').value,
                prompt_2: document.getElementById('prompt_2').value,
                negative_prompt: document.getElementById('negative_prompt').value,
                width: document.getElementById('width').value,
                height: document.getElementById('height').value,
                steps: document.getElementById('steps').value,
                prompt_strength: document.getElementById('prompt_strength').value,
                seed: document.getElementById('seed').value,
                customDescriptorCount: document.getElementById('customDescriptorCount').value,
                customDescriptors: customDescriptors,
                // Standard lists
                races: currentRaces,
                sexes: currentSexes,
                ages: currentAges,
                shotTypes: currentShotTypes,
                hairColors: currentHairColors,
                bodyTypes: currentBodyTypes,
                emotions: currentEmotions,
                clothing: currentClothing
            };
            localStorage.setItem('generationSettings', JSON.stringify(dataToSave));
        }

        function loadFromLocalStorage() {
            const savedData = localStorage.getItem('generationSettings');
            if (savedData) {
                const data = JSON.parse(savedData);
                document.getElementById('prompt_1').value = data.prompt_1 || '';
                document.getElementById('prompt_2').value = data.prompt_2 || '';
                document.getElementById('negative_prompt').value = data.negative_prompt || '';
                document.getElementById('width').value = data.width || '1024';
                document.getElementById('height').value = data.height || '1024';
                document.getElementById('steps').value = data.steps || '30';
                document.getElementById('prompt_strength').value = data.prompt_strength || '7.5';
                document.getElementById('seed').value = data.seed || '-1';

                // Load batch and increase settings
                document.getElementById('batchEnabled').checked = data.batchEnabled || false;
                document.getElementById('batchCount').value = data.batchCount || '1';
                document.getElementById('stepIncreaseEnabled').checked = data.stepIncreaseEnabled || false;
                document.getElementById('stepIncrease').value = data.stepIncrease || '5';
                document.getElementById('strengthIncreaseEnabled').checked = data.strengthIncreaseEnabled || false;
                document.getElementById('strengthIncrease').value = data.strengthIncrease || '0.5';

                // Update disabled states
                document.getElementById('batchCount').disabled = !data.batchEnabled;
                document.getElementById('stepIncreaseEnabled').disabled = !data.batchEnabled;
                document.getElementById('strengthIncreaseEnabled').disabled = !data.batchEnabled;
                updateStepIncreaseState();
                updateStrengthIncreaseState();

                // Load custom descriptor count and data
                if (data.customDescriptorCount) {
                    document.getElementById('customDescriptorCount').value = data.customDescriptorCount;
                }

                if (data.customDescriptors && Array.isArray(data.customDescriptors)) {
                    customDescriptors = data.customDescriptors;
                }

                // Load standard lists
                if (data.races) currentRaces = data.races;
                if (data.sexes) currentSexes = data.sexes;
                if (data.ages) currentAges = data.ages;
                if (data.shotTypes) currentShotTypes = data.shotTypes;
                if (data.hairColors) currentHairColors = data.hairColors;
                if (data.bodyTypes) currentBodyTypes = data.bodyTypes;
                if (data.emotions) currentEmotions = data.emotions;
                if (data.clothing) currentClothing = data.clothing;

                // Update displays
                updateRaceDisplay();
                updateSexDisplay();
                updateAgeDisplay();
                updateShotTypesDisplay();
                updateHairColorsDisplay();
                updateBodyTypesDisplay();
                updateEmotionsDisplay();
                updateClothingDisplay();

                // Generate custom descriptor fields
                generateCustomDescriptorFields();

                updateTokenCount();
            }
        }

        function updateRaceList() {
            const raceInput = document.getElementById('raceList').value;
            currentRaces = raceInput
                .split('\n')
                .map(race => race.trim())
                .filter(race => race); // Remove empty lines
            updateRaceDisplay();
            saveToLocalStorage();
        }

        function updateRaceDisplay() {
            document.getElementById('racesDisplay').textContent = currentRaces.join(', ');
        }

        function updateSexList() {
            const sexInput = document.getElementById('sexList').value;
            currentSexes = sexInput
                .split('\n')
                .map(sex => sex.trim())
                .filter(sex => sex); // Remove empty lines
            updateSexDisplay();
            saveToLocalStorage();
        }

        function updateSexDisplay() {
            document.getElementById('sexesDisplay').textContent = currentSexes.join(', ');
        }

        function updateAgeList() {
            const ageInput = document.getElementById('ageList').value;
            currentAges = ageInput
                .split('\n')
                .map(age => age.trim())
                .filter(age => age); // Only filter empty lines, accept any non-empty string
            updateAgeDisplay();
            saveToLocalStorage();
        }

        function updateAgeDisplay() {
            document.getElementById('ageRangeDisplay').textContent = currentAges.join(', ');
        }

        function updateShotTypes() {
            const shotInput = document.getElementById('shotTypeList').value;
            currentShotTypes = shotInput
                .split('\n')
                .map(shot => shot.trim())
                .filter(shot => shot); // Remove empty lines
            updateShotTypesDisplay();
            saveToLocalStorage();
        }

        function updateShotTypesDisplay() {
            document.getElementById('shotTypesDisplay').textContent = currentShotTypes.join(', ');
        }

        function updateHairColors() {
            const hairInput = document.getElementById('hairColorList').value;
            currentHairColors = hairInput
                .split('\n')
                .map(color => color.trim())
                .filter(color => color);
            updateHairColorsDisplay();
            saveToLocalStorage();
        }

        function updateBodyTypes() {
            const bodyInput = document.getElementById('bodyTypeList').value;
            currentBodyTypes = bodyInput
                .split('\n')
                .map(type => type.trim())
                .filter(type => type);
            updateBodyTypesDisplay();
            saveToLocalStorage();
        }

        function updateHairColorsDisplay() {
            document.getElementById('hairColorsDisplay').textContent = currentHairColors.join(', ');
        }

        function updateBodyTypesDisplay() {
            document.getElementById('bodyTypesDisplay').textContent = currentBodyTypes.join(', ');
        }

        function updateEmotions() {
            const emotionInput = document.getElementById('emotionList').value;
            currentEmotions = emotionInput
                .split('\n')
                .map(emotion => emotion.trim())
                .filter(emotion => emotion); // Remove empty lines
            updateEmotionsDisplay();
            saveToLocalStorage();
        }

        function updateEmotionsDisplay() {
            document.getElementById('emotionsDisplay').textContent = currentEmotions.join(', ');
        }

        // The old individual update functions have been replaced by the dynamic updateCustomDescriptor function

        // Function to generate custom descriptor fields based on the count
        function generateCustomDescriptorFields() {
            const count = parseInt(document.getElementById('customDescriptorCount').value);
            if (isNaN(count) || count < 1) {
                alert('Please enter a valid number of custom descriptors (minimum 1)');
                return;
            }

            // Limit to maximum
            const descriptorCount = Math.min(count, maxCustomDescriptors);
            document.getElementById('customDescriptorCount').value = descriptorCount;

            // Save the count to localStorage
            const savedData = localStorage.getItem('generationSettings');
            const dataToSave = savedData ? JSON.parse(savedData) : {};
            dataToSave.customDescriptorCount = descriptorCount;
            localStorage.setItem('generationSettings', JSON.stringify(dataToSave));

            // Initialize the customDescriptors array if needed
            if (customDescriptors.length === 0) {
                customDescriptors = new Array(maxCustomDescriptors).fill([]);
            }

            // Generate the HTML for the custom descriptor fields
            const container = document.getElementById('customDescriptorsContainer');
            container.innerHTML = '';
            container.style.display = 'flex';
            container.style.flexWrap = 'wrap';
            container.style.gap = '15px';

            // Create individual field containers
            for (let i = 0; i < descriptorCount; i++) {
                const fieldContainer = document.createElement('div');
                fieldContainer.style.width = '300px'; // Fixed width of 300px
                fieldContainer.style.marginBottom = '15px';

                const label = document.createElement('label');
                label.textContent = `Custom Descriptors ${i + 1}:`;
                label.style.display = 'block';
                label.style.marginBottom = '5px';
                fieldContainer.appendChild(label);

                const textarea = document.createElement('textarea');
                textarea.id = `customDescriptor${i + 1}List`;
                textarea.rows = 5;
                textarea.style.width = '100%';
                textarea.style.resize = 'none';
                textarea.placeholder = 'Enter each descriptor on a new line';

                // Load saved values if available
                if (customDescriptors[i] && customDescriptors[i].length > 0) {
                    textarea.value = customDescriptors[i].join('\n');
                }

                fieldContainer.appendChild(textarea);

                const button = document.createElement('button');
                button.textContent = `Update Custom Descriptors ${i + 1}`;
                button.style.marginTop = '5px';
                button.style.width = '100%';
                button.onclick = function() { updateCustomDescriptor(i + 1); };
                fieldContainer.appendChild(button);

                container.appendChild(fieldContainer);
            }

            // Update the display
            updateCustomDescriptorsDisplay();
        }

        // Function to update a specific custom descriptor
        function updateCustomDescriptor(index) {
            const textareaId = `customDescriptor${index}List`;
            const textarea = document.getElementById(textareaId);
            if (!textarea) return;

            customDescriptors[index - 1] = textarea.value
                .split('\n')
                .map(descriptor => descriptor.trim())
                .filter(descriptor => descriptor);

            updateCustomDescriptorsDisplay();
            saveToLocalStorage();
        }

        // Function to update the display of all custom descriptors
        function updateCustomDescriptorsDisplay() {
            const customDescriptorsDisplay = document.getElementById('customDescriptorsDisplay');
            const count = parseInt(document.getElementById('customDescriptorCount').value);

            let html = '';
            for (let i = 0; i < count; i++) {
                if (customDescriptors[i] && customDescriptors[i].length > 0) {
                    html += `<div>Custom ${i + 1}: ${customDescriptors[i].join(', ')}</div>`;
                }
            }

            customDescriptorsDisplay.innerHTML = html;
        }

        function updateClothing() {
            const clothingInput = document.getElementById('clothingList').value;
            currentClothing = clothingInput
                .split('\n')
                .map(clothing => clothing.trim())
                .filter(clothing => clothing);
            updateClothingDisplay();
            saveToLocalStorage();
        }

        function updateClothingDisplay() {
            document.getElementById('clothingDisplay').textContent = currentClothing.join(', ');
        }

        // Add this to your existing DOMContentLoaded event listener
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Load checkpoints first
                await loadCheckpoints();

                // Add checkpoint change listener
                document.getElementById('checkpoint').addEventListener('change', async function() {
                    await switchCheckpoint();
                });

                // Then load everything else
                loadFromLocalStorage();
                await loadLoras();
                updateTokenCount();

            } catch (error) {
                console.error('Initialization error:', error);
            }
        });

        function replaceAllTokens(prompt) {
            // Get all the standard lists
            const ages = document.getElementById('ageList').value.split('\n').filter(x => x.trim());
            const races = document.getElementById('raceList').value.split('\n').filter(x => x.trim());
            const sexes = document.getElementById('sexList').value.split('\n').filter(x => x.trim());
            const shots = document.getElementById('shotTypeList').value.split('\n').filter(x => x.trim());
            const hairColors = document.getElementById('hairColorList').value.split('\n').filter(x => x.trim());
            const bodyTypes = document.getElementById('bodyTypeList').value.split('\n').filter(x => x.trim());
            const emotions = document.getElementById('emotionList').value.split('\n').filter(x => x.trim());
            const clothing = document.getElementById('clothingList').value.split('\n').filter(x => x.trim());

            // Get dynamic custom descriptor lists
            const count = parseInt(document.getElementById('customDescriptorCount').value);
            const descriptorCount = Math.min(count, maxCustomDescriptors);

            // Update customDescriptors array with current values from textareas
            for (let i = 0; i < descriptorCount; i++) {
                const textarea = document.getElementById(`customDescriptor${i + 1}List`);
                if (textarea) {
                    customDescriptors[i] = textarea.value
                        .split('\n')
                        .map(descriptor => descriptor.trim())
                        .filter(descriptor => descriptor);
                }
            }

            // Do 10 passes of replacement to handle nested tokens
            let result = prompt;
            for (let i = 0; i < 10; i++) {
                // Replace standard tokens
                result = result
                    .replace(/AGE/g, ages.length ? ages[Math.floor(Math.random() * ages.length)] : 'AGE')
                    .replace(/RACE/g, races.length ? races[Math.floor(Math.random() * races.length)] : 'RACE')
                    .replace(/SEX/g, sexes.length ? sexes[Math.floor(Math.random() * sexes.length)] : 'SEX')
                    .replace(/SHOT/g, shots.length ? shots[Math.floor(Math.random() * shots.length)] : 'SHOT')
                    .replace(/HAIR/g, hairColors.length ? hairColors[Math.floor(Math.random() * hairColors.length)] : 'HAIR')
                    .replace(/BODY/g, bodyTypes.length ? bodyTypes[Math.floor(Math.random() * bodyTypes.length)] : 'BODY')
                    .replace(/EMO/g, emotions.length ? emotions[Math.floor(Math.random() * emotions.length)] : 'EMO')
                    .replace(/CLOTHS/g, clothing.length ? clothing[Math.floor(Math.random() * clothing.length)] : 'CLOTHS');

                // Replace dynamic custom tokens - sort by length to handle longer tokens first
                // Create an array of indices sorted by token length (descending)
                const indices = Array.from({ length: descriptorCount }, (_, i) => i)
                    .sort((a, b) => String(b + 1).length - String(a + 1).length);

                // Replace tokens in order of length (longest first)
                for (const j of indices) {
                    // Use word boundary to ensure we match complete tokens
                    const cusToken = new RegExp(`\\bCUS${j + 1}\\b`, 'g');
                    const cusList = customDescriptors[j] || [];
                    result = result.replace(cusToken, cusList.length ?
                        cusList[Math.floor(Math.random() * cusList.length)] :
                        `CUS${j + 1}`);
                }
            }
            return result;
        }

        // Main generation function
        async function generate() {
            try {
                const batchEnabled = document.getElementById('batchEnabled').checked;
                const batchCount = batchEnabled ? parseInt(document.getElementById('batchCount').value) : 1;
                const generationType = document.getElementById('generation_type').value;

                // Step increase settings
                const stepIncreaseEnabled = document.getElementById('stepIncreaseEnabled').checked;
                const stepIncrease = stepIncreaseEnabled ? parseInt(document.getElementById('stepIncrease').value) : 0;
                const baseSteps = parseInt(document.getElementById('steps').value);

                // Strength increase settings
                const strengthIncreaseEnabled = document.getElementById('strengthIncreaseEnabled').checked;
                const strengthIncrease = strengthIncreaseEnabled ? parseFloat(document.getElementById('strengthIncrease').value) : 0;
                const baseStrength = parseFloat(document.getElementById('prompt_strength').value);

                // Clear previous prompt log
                document.getElementById('promptLog').innerHTML = '';

                // Log the generation type and increase info
                document.getElementById('promptLog').innerHTML += `<div>Generation Type: ${generationType}</div>`;
                if (stepIncreaseEnabled && batchEnabled) {
                    document.getElementById('promptLog').innerHTML += `<div style="color: #007bff;">📈 Step Increase: Starting at ${baseSteps} steps, increasing by ${stepIncrease} per batch</div>`;
                }
                if (strengthIncreaseEnabled && batchEnabled) {
                    document.getElementById('promptLog').innerHTML += `<div style="color: #28a745;">💪 Strength Increase: Starting at ${baseStrength} CFG, increasing by ${strengthIncrease} per batch</div>`;
                }

                // Generate images
                for (let i = 0; i < batchCount; i++) {
                    // Calculate current steps and strength for this batch
                    const currentSteps = stepIncreaseEnabled ? baseSteps + (i * stepIncrease) : baseSteps;
                    const currentStrength = strengthIncreaseEnabled ? baseStrength + (i * strengthIncrease) : baseStrength;

                    // Log batch info
                    let batchInfo = `🎯 Batch ${i + 1}/${batchCount}:`;
                    if (stepIncreaseEnabled) batchInfo += ` ${currentSteps} steps`;
                    if (strengthIncreaseEnabled) batchInfo += ` ${currentStrength.toFixed(1)} CFG`;
                    if (stepIncreaseEnabled || strengthIncreaseEnabled) {
                        document.getElementById('promptLog').innerHTML += `<div style="color: #28a745;">${batchInfo}</div>`;
                    }
                    // Get both prompts
                    let prompt1 = document.getElementById('prompt_1').value;
                    let prompt2 = document.getElementById('prompt_2').value;

                    // Replace all tokens in both prompts
                    prompt1 = replaceAllTokens(prompt1);
                    prompt2 = replaceAllTokens(prompt2);

                    // Log the randomized prompts
                    document.getElementById('promptLog').innerHTML += `<div>Batch ${i + 1}:<br>Prompt 1: ${prompt1}<br>Prompt 2: ${prompt2}</div>`;

                    const formData = new FormData();
                    formData.append('prompt_1', prompt1);  // Send replaced prompt
                    formData.append('prompt_2', prompt2);  // Send replaced prompt
                    formData.append('negative_prompt', document.getElementById('negative_prompt').value || '');
                    formData.append('loras', JSON.stringify(selectedLoras));
                    formData.append('seed', getSeedValue());

                    // Handle different generation types
                    if (generationType === 'txt2img') {
                        // Text to image specific parameters
                        formData.append('width', document.getElementById('width').value);
                        formData.append('height', document.getElementById('height').value);
                        formData.append('inference_steps', currentSteps); // Use calculated steps
                        formData.append('prompt_strength', currentStrength); // Use calculated strength
                        formData.append('batch_enabled', batchEnabled);
                        formData.append('batch_count', batchCount);
                    } 
                    else if (generationType === 'img2img') {
                        // Image to image specific parameters
                        formData.append('width', document.getElementById('width').value);
                        formData.append('height', document.getElementById('height').value);
                        formData.append('inference_steps', currentSteps); // Use calculated steps
                        formData.append('prompt_strength', currentStrength); // Use calculated strength
                        formData.append('batch_enabled', batchEnabled);
                        formData.append('batch_count', batchCount);
                        
                        // Get the template image
                        const templateImageElement = document.getElementById('templateImage');
                        if (!templateImageElement) {
                            throw new Error("Template image element not found. ID 'templateImage' might be incorrect.");
                        }
                        
                        const templateImage = templateImageElement.files[0];
                        if (!templateImage) {
                            alert('Please select a template image for Image-to-Image generation');
                            return;
                        }
                        
                        const templateStrengthElement = document.getElementById('templateStrength');
                        if (!templateStrengthElement) {
                            throw new Error("Template strength element not found. ID 'templateStrength' might be incorrect.");
                        }
                        
                        formData.append('template_image', templateImage);
                        formData.append('template_strength', templateStrengthElement.value);
                    }
                    else if (generationType === 'animate') {
                        // Get selected animation type
                        const animationType = document.querySelector('input[name="animation_type"]:checked').value;

                        if (animationType === 'svd') {
                            // SVD animation parameters
                            const svdImageElement = document.getElementById('svd_image');
                            if (!svdImageElement) {
                                throw new Error("SVD image element not found. ID 'svd_image' might be incorrect.");
                            }

                            const svdFile = svdImageElement.files[0];
                            if (!svdFile) {
                                alert('Please select an image for SVD animation');
                                return;
                            }

                            const svdFramesElement = document.getElementById('svd_frames');
                            const motionBucketElement = document.getElementById('motion_bucket_id');
                            const svdFpsElement = document.getElementById('svd_fps');
                            const decodeChunkElement = document.getElementById('decode_chunk_size');

                            if (!svdFramesElement || !motionBucketElement || !svdFpsElement || !decodeChunkElement) {
                                throw new Error("Missing SVD animation controls");
                            }

                            formData.append('file', svdFile);
                            formData.append('num_frames', svdFramesElement.value);
                            formData.append('motion_bucket_id', motionBucketElement.value);
                            formData.append('fps', svdFpsElement.value);
                            formData.append('decode_chunk_size', decodeChunkElement.value);

                            document.getElementById('promptLog').innerHTML += `<div>SVD Animation: ${svdFramesElement.value} frames at ${svdFpsElement.value} fps, motion intensity: ${motionBucketElement.value}</div>`;

                        } else if (animationType === 'animatediff') {
                            // AnimateDiff animation parameters (text-to-video, no image needed)
                            const animatediffFramesElement = document.getElementById('animatediff_frames');
                            const animatediffWidthElement = document.getElementById('animatediff_width');
                            const animatediffHeightElement = document.getElementById('animatediff_height');
                            const animatediffStepsElement = document.getElementById('animatediff_steps');
                            const animatediffFpsElement = document.getElementById('animatediff_fps');

                            if (!animatediffFramesElement || !animatediffWidthElement || !animatediffHeightElement || !animatediffStepsElement || !animatediffFpsElement) {
                                throw new Error("Missing AnimateDiff animation controls");
                            }

                            formData.append('num_frames', animatediffFramesElement.value);
                            formData.append('width', animatediffWidthElement.value);
                            formData.append('height', animatediffHeightElement.value);
                            formData.append('num_inference_steps', animatediffStepsElement.value);
                            formData.append('fps', animatediffFpsElement.value);
                            formData.append('guidance_scale', document.getElementById('prompt_strength').value);

                            document.getElementById('promptLog').innerHTML += `<div>AnimateDiff Animation: ${animatediffFramesElement.value} frames at ${animatediffFpsElement.value} fps, size: ${animatediffWidthElement.value}x${animatediffHeightElement.value}</div>`;

                        } else if (animationType === 'legacy') {
                            // Legacy animation parameters
                            const legacyImageElement = document.getElementById('legacy_image');
                            if (!legacyImageElement) {
                                throw new Error("Legacy image element not found. ID 'legacy_image' might be incorrect.");
                            }

                            const legacyFile = legacyImageElement.files[0];
                            if (!legacyFile) {
                                alert('Please select an image for legacy animation');
                                return;
                            }

                            const legacyFramesElement = document.getElementById('legacy_frames');
                            const animateStepsElement = document.getElementById('animateSteps');
                            const animateStrengthElement = document.getElementById('animateStrength');
                            const legacyFramerateElement = document.getElementById('legacy_framerate');
                            const promptStrengthElement = document.getElementById('prompt_strength');

                            if (!legacyFramesElement || !animateStepsElement || !animateStrengthElement || !legacyFramerateElement || !promptStrengthElement) {
                                throw new Error("Missing legacy animation controls");
                            }

                            formData.append('file', legacyFile);
                            formData.append('num_frames', legacyFramesElement.value);
                            formData.append('animateInferenceSteps', animateStepsElement.value);
                            formData.append('strength', animateStrengthElement.value);
                            formData.append('framerate', legacyFramerateElement.value);
                            formData.append('prompt_strength', promptStrengthElement.value);

                            document.getElementById('promptLog').innerHTML += `<div>Legacy Animation: ${legacyFramesElement.value} frames at ${legacyFramerateElement.value} fps</div>`;
                        }
                    }

                    // Send to backend
                    let endpoint = '/generate';
                    if (generationType === 'animate') {
                        const animationType = document.querySelector('input[name="animation_type"]:checked').value;
                        if (animationType === 'svd') {
                            endpoint = '/animate';
                        } else if (animationType === 'animatediff') {
                            endpoint = '/animate_text';
                        } else if (animationType === 'legacy') {
                            endpoint = '/animate_legacy';
                        }
                    }
                    document.getElementById('promptLog').innerHTML += `<div>Sending request to: ${endpoint}</div>`;

                    const response = await fetch(endpoint, {
                        method: 'POST',
                        body: formData
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(`Server error: ${errorData.detail || response.statusText}`);
                    }

                    const result = await response.json();
                    
                    // Display generation info without showing the actual files (to avoid file locking)
                    if (generationType === 'animate') {
                        // For animation, show path info only
                        document.getElementById('promptLog').innerHTML += `<div style="color: #28a745;"><strong>✅ Video generated: ${result.video_path}</strong></div>`;

                        // Display seed used
                        if (result.seed_used !== undefined) {
                            document.getElementById('promptLog').innerHTML += `<div style="color: #007bff;"><strong>Seed used: ${result.seed_used}</strong></div>`;
                        }
                    } else {
                        // For images, show path info only
                        document.getElementById('promptLog').innerHTML += `<div style="color: #28a745;"><strong>✅ Image generated: ${result.image_path}</strong></div>`;

                        // Display seed used
                        if (result.seed_used !== undefined) {
                            document.getElementById('promptLog').innerHTML += `<div style="color: #007bff;"><strong>Seed used: ${result.seed_used}</strong></div>`;
                        }
                    }
                }
            } catch (error) {
                console.error('Generation error:', error);
                document.getElementById('promptLog').innerHTML += `<div class="error">Error: ${error.message}</div>`;
            }
        }

        // Load available checkpoints on page load
        document.addEventListener('DOMContentLoaded', async () => {
            await loadCheckpoints();

            // Add change event listener to checkpoint select
            document.getElementById('checkpoint').addEventListener('change', async function() {
                await switchCheckpoint();
            });
        });

        let checkpointInitialized = false;

        async function loadCheckpoints() {
            try {
                console.log("Fetching checkpoints...");
                const response = await fetch('/checkpoints');
                const data = await response.json();
                console.log("Received checkpoints:", data);

                const checkpointSelect = document.getElementById('checkpoint');
                if (!checkpointSelect) {
                    console.error('Checkpoint select element not found');
                    return;
                }

                checkpointSelect.innerHTML = `
                    <option value="base_sdxl">Base SDXL Model</option>
                    ${data.checkpoints
                        .filter(cp => cp !== "base_sdxl")
                        .map(checkpoint =>
                            `<option value="${checkpoint}">${checkpoint}</option>`
                        ).join('')}
                `;
                console.log("Updated checkpoint options:", checkpointSelect.innerHTML);
                checkpointInitialized = true;
            } catch (error) {
                console.error('Failed to load checkpoints:', error);
            }
        }

        async function switchCheckpoint() {
            const checkpoint = document.getElementById('checkpoint').value;
            const statusDiv = document.getElementById('checkpoint_status');
            const loadingIndicator = document.getElementById('loading_indicator');

            try {
                // Show loading state
                statusDiv.textContent = 'Switching checkpoint...';
                statusDiv.style.color = 'blue';
                if (loadingIndicator) loadingIndicator.style.display = 'block';

                const formData = new FormData();
                formData.append('checkpoint_name', checkpoint);

                const response = await fetch('/switch_checkpoint', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.detail?.error || data.detail || 'Failed to switch checkpoint');
                }

                // Success state
                statusDiv.textContent = data.message || 'Successfully switched checkpoint';
                statusDiv.style.color = 'green';

                // Optionally refresh the UI or update other elements
                if (checkpoint === 'base_sdxl') {
                    document.getElementById('model_indicator').textContent = 'Base SDXL';
                } else {
                    document.getElementById('model_indicator').textContent = checkpoint;
                }

            } catch (error) {
                console.error('Error switching checkpoint:', error);
                statusDiv.textContent = `Error: ${error.message}`;
                statusDiv.style.color = 'red';
            } finally {
                if (loadingIndicator) loadingIndicator.style.display = 'none';
            }
        }

        async function checkLoadedState() {
            try {
                const response = await fetch('/check_loaded_state', {
                    method: 'GET'
                });
                const result = await response.json();
                alert(result.message);
            } catch (error) {
                alert('Error checking loaded state: ' + error);
            }
        }
    </script>
    <script>
        function testGenerate() {
            console.log('Generate button clicked - starting debug process');
            document.getElementById('promptLog').innerHTML += '<div>Starting debug process...</div>';

            // Test all form elements
            const debugInfo = {
                prompt1: document.getElementById('prompt_1')?.value,
                prompt2: document.getElementById('prompt_2')?.value,
                width: document.getElementById('width')?.value,
                height: document.getElementById('height')?.value,
                steps: document.getElementById('steps')?.value,
                promptStrength: document.getElementById('prompt_strength')?.value,
                batchEnabled: document.getElementById('batchEnabled')?.checked,
                batchCount: document.getElementById('batchCount')?.value,
                stepIncreaseEnabled: document.getElementById('stepIncreaseEnabled')?.checked,
                stepIncrease: document.getElementById('stepIncrease')?.value,
                strengthIncreaseEnabled: document.getElementById('strengthIncreaseEnabled')?.checked,
                strengthIncrease: document.getElementById('strengthIncrease')?.value
            };

            console.log('Debug info:', debugInfo);
            document.getElementById('promptLog').innerHTML += `<div>Form values: ${JSON.stringify(debugInfo, null, 2)}</div>`;

            // Now try to call the actual generate function
            try {
                console.log('Attempting to call generate()');
                document.getElementById('promptLog').innerHTML += '<div>Attempting to call generate()...</div>';
                generate();
            } catch (error) {
                console.error('Error in generate():', error);
                document.getElementById('promptLog').innerHTML += `<div style="color: red;">Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>









