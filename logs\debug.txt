2025-07-18 00:55:46,543 | debug_logger | DEBUG | Debug logging system initialized
2025-07-18 00:55:46,583 | debug_logger | DEBUG | Debug logging system initialized
2025-07-18 00:56:15,860 | debug_logger | INFO | 
=== Starting new generation request ===
2025-07-18 00:56:15,861 | debug_logger | INFO | Primary Prompt: Couples Adventure: (16 year old girl with Black hair and amber eyes wearing a Green and Purple and Black cotton kimono). AND (19 year old boy with Black hair and amber eyes wearing a Green and Purple and Black cotton kimono).  Blowjobs in the park
2025-07-18 00:56:15,861 | debug_logger | INFO | Secondary Prompt: (8K UHD), (Intricate Details), (Extremely Detailed), ((MuppetShow))
2025-07-18 00:56:15,861 | debug_logger | INFO | Parameters: width=1024, height=1024, steps=25, strength=10.0
2025-07-18 00:56:15,863 | debug_logger | INFO | Parsed LoRA data: [
  {
    "path": "SDXL-MuppetShow-Lora-step00002500.safetensors",
    "weight": 0.75
  }
]
2025-07-18 00:56:15,863 | debug_logger | INFO | 
=== Loading LoRAs ===
2025-07-18 00:56:15,863 | debug_logger | INFO | Before loading LoRAs - Memory status:
2025-07-18 00:56:15,863 | debug_logger | INFO | Allocated: 0.00MB
2025-07-18 00:56:15,864 | debug_logger | INFO | Reserved: 2.00MB
2025-07-18 00:56:15,864 | debug_logger | INFO | 
=== Starting Multiple LoRA Loading Process ===
2025-07-18 00:56:15,891 | debug_logger | INFO | Cleared existing LoRA weights
2025-07-18 00:56:15,892 | debug_logger | INFO | 
Loading LoRA: SDXL-MuppetShow-Lora-step00002500.safetensors
2025-07-18 00:56:15,892 | debug_logger | INFO | Weight: 0.75
2025-07-18 00:56:17,445 | debug_logger | INFO | Successfully loaded LoRA: SDXL-MuppetShow-Lora-step00002500.safetensors
2025-07-18 00:56:17,446 | debug_logger | INFO | LoRAs loaded successfully
2025-07-18 00:56:17,446 | debug_logger | INFO | After loading LoRAs - Memory status:
2025-07-18 00:56:17,446 | debug_logger | INFO | Allocated: 0.00MB
2025-07-18 00:56:17,447 | debug_logger | INFO | Reserved: 2.00MB
2025-07-18 00:56:17,483 | debug_logger | INFO | 
=== Starting Generation ===
2025-07-18 00:56:17,484 | debug_logger | INFO | Generation parameters:
2025-07-18 00:56:17,484 | debug_logger | INFO | - Width: 1024
2025-07-18 00:56:17,484 | debug_logger | INFO | - Height: 1024
2025-07-18 00:56:17,484 | debug_logger | INFO | - Steps: 25
2025-07-18 00:56:17,485 | debug_logger | INFO | - Prompt strength: 10.0
2025-07-18 00:56:17,561 | debug_logger | INFO | Starting txt2img generation...
2025-07-18 00:56:17,562 | debug_logger | INFO | Prompts being sent to pipeline:
2025-07-18 00:56:17,562 | debug_logger | INFO | Prompt 1: 'Couples Adventure: (16 year old girl with Black hair and amber eyes wearing a Green and Purple and Black cotton kimono). AND (19 year old boy with Black hair and amber eyes wearing a Green and Purple and Black cotton kimono).  Blowjobs in the park'
2025-07-18 00:56:17,562 | debug_logger | INFO | Prompt 2: '(8K UHD), (Intricate Details), (Extremely Detailed), ((MuppetShow))'
2025-07-18 00:56:17,563 | debug_logger | INFO | Processing with prompts:
2025-07-18 00:56:17,563 | debug_logger | INFO | Prompt 1: Couples Adventure: (16 year old girl with Black hair and amber eyes wearing a Green and Purple and Black cotton kimono). AND (19 year old boy with Black hair and amber eyes wearing a Green and Purple and Black cotton kimono).  Blowjobs in the park
2025-07-18 00:56:17,563 | debug_logger | INFO | Prompt 2: (8K UHD), (Intricate Details), (Extremely Detailed), ((MuppetShow))
2025-07-18 00:56:26,152 | debug_logger | ERROR | 
=== Generation Process Failed ===
2025-07-18 00:56:26,153 | debug_logger | ERROR | Error occurred during the pipeline call
2025-07-18 00:56:26,153 | debug_logger | ERROR | Error type: OutOfMemoryError
2025-07-18 00:56:26,153 | debug_logger | ERROR | Error message: CUDA out of memory. Tried to allocate 160.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 5.37 GiB is allocated by PyTorch, and 337.78 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-07-18 00:55:46,583 | debug_logger | DEBUG | Debug logging system initialized
2025-07-18 00:56:15,860 | debug_logger | INFO | 
=== Starting new generation request ===
2025-07-18 00:56:15,861 | debug_logger | INFO | Primary Prompt: Couples Adventure: (16 year old girl with Black hair and amber eyes wearing a Green and Purple and Black cotton kimono). AND (19 year old boy with Black hair and amber eyes wearing a Green and Purple and Black cotton kimono).  Blowjobs in the park
2025-07-18 00:56:15,861 | debug_logger | INFO | Secondary Prompt: (8K UHD), (Intricate Details), (Extremely Detailed), ((MuppetShow))
2025-07-18 00:56:15,861 | debug_logger | INFO | Parameters: width=1024, height=1024, steps=25, strength=10.0
2025-07-18 00:56:15,863 | debug_logger | INFO | Parsed LoRA data: [
  {
    "path": "SDXL-MuppetShow-Lora-step00002500.safetensors",
    "weight": 0.75
  }
]
2025-07-18 00:56:15,863 | debug_logger | INFO | 
=== Loading LoRAs ===
2025-07-18 00:56:15,863 | debug_logger | INFO | Before loading LoRAs - Memory status:
2025-07-18 00:56:15,863 | debug_logger | INFO | Allocated: 0.00MB
2025-07-18 00:56:15,864 | debug_logger | INFO | Reserved: 2.00MB
2025-07-18 00:56:15,864 | debug_logger | INFO | 
=== Starting Multiple LoRA Loading Process ===
2025-07-18 00:56:15,891 | debug_logger | INFO | Cleared existing LoRA weights
2025-07-18 00:56:15,892 | debug_logger | INFO | 
Loading LoRA: SDXL-MuppetShow-Lora-step00002500.safetensors
2025-07-18 00:56:15,892 | debug_logger | INFO | Weight: 0.75
2025-07-18 00:56:17,445 | debug_logger | INFO | Successfully loaded LoRA: SDXL-MuppetShow-Lora-step00002500.safetensors
2025-07-18 00:56:17,446 | debug_logger | INFO | LoRAs loaded successfully
2025-07-18 00:56:17,446 | debug_logger | INFO | After loading LoRAs - Memory status:
2025-07-18 00:56:17,446 | debug_logger | INFO | Allocated: 0.00MB
2025-07-18 00:56:17,447 | debug_logger | INFO | Reserved: 2.00MB
2025-07-18 00:56:17,483 | debug_logger | INFO | 
=== Starting Generation ===
2025-07-18 00:56:17,484 | debug_logger | INFO | Generation parameters:
2025-07-18 00:56:17,484 | debug_logger | INFO | - Width: 1024
2025-07-18 00:56:17,484 | debug_logger | INFO | - Height: 1024
2025-07-18 00:56:17,484 | debug_logger | INFO | - Steps: 25
2025-07-18 00:56:17,485 | debug_logger | INFO | - Prompt strength: 10.0
2025-07-18 00:56:17,561 | debug_logger | INFO | Starting txt2img generation...
2025-07-18 00:56:17,562 | debug_logger | INFO | Prompts being sent to pipeline:
2025-07-18 00:56:17,562 | debug_logger | INFO | Prompt 1: 'Couples Adventure: (16 year old girl with Black hair and amber eyes wearing a Green and Purple and Black cotton kimono). AND (19 year old boy with Black hair and amber eyes wearing a Green and Purple and Black cotton kimono).  Blowjobs in the park'
2025-07-18 00:56:17,562 | debug_logger | INFO | Prompt 2: '(8K UHD), (Intricate Details), (Extremely Detailed), ((MuppetShow))'
2025-07-18 00:56:17,563 | debug_logger | INFO | Processing with prompts:
2025-07-18 00:56:17,563 | debug_logger | INFO | Prompt 1: Couples Adventure: (16 year old girl with Black hair and amber eyes wearing a Green and Purple and Black cotton kimono). AND (19 year old boy with Black hair and amber eyes wearing a Green and Purple and Black cotton kimono).  Blowjobs in the park
2025-07-18 00:56:17,563 | debug_logger | INFO | Prompt 2: (8K UHD), (Intricate Details), (Extremely Detailed), ((MuppetShow))
2025-07-18 00:56:26,152 | debug_logger | ERROR | 
=== Generation Process Failed ===
2025-07-18 00:56:26,153 | debug_logger | ERROR | Error occurred during the pipeline call
2025-07-18 00:56:26,153 | debug_logger | ERROR | Error type: OutOfMemoryError
2025-07-18 00:56:26,153 | debug_logger | ERROR | Error message: CUDA out of memory. Tried to allocate 160.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 5.37 GiB is allocated by PyTorch, and 337.78 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-07-18 00:56:26,157 | debug_logger | ERROR | Traceback:
Traceback (most recent call last):
  File "C:\SDXL_ArtGen\bigapp.py", line 735, in generate_image
    image = pipeline(
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\utils\_contextlib.py", line 115, in decorate_context
    return func(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py", line 1216, in __call__
    noise_pred = self.unet(
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\accelerate\hooks.py", line 165, in new_forward
    output = module._old_forward(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\models\unet_2d_condition.py", line 1177, in forward
    sample = upsample_block(
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\models\unet_2d_blocks.py", line 2354, in forward
    hidden_states = attn(
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\models\transformer_2d.py", line 392, in forward
    hidden_states = block(
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\models\attention.py", line 393, in forward
    ff_output = self.ff(norm_hidden_states, scale=lora_scale)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\models\attention.py", line 665, in forward
    hidden_states = module(hidden_states, scale)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\models\activations.py", line 102, in forward
    hidden_states, gate = self.proj(hidden_states, *args).chunk(2, dim=-1)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\peft\tuners\lora\layer.py", line 319, in forward
    result += lora_B(lora_A(dropout(x))) * scaling
torch.cuda.OutOfMemoryError: CUDA out of memory. Tried to allocate 160.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 5.37 GiB is allocated by PyTorch, and 337.78 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)

2025-07-18 00:56:26,157 | debug_logger | ERROR | 
=== Generation Error ===
2025-07-18 00:56:26,157 | debug_logger | ERROR | Error type: OutOfMemoryError
2025-07-18 00:56:26,158 | debug_logger | ERROR | Error message: CUDA out of memory. Tried to allocate 160.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 5.37 GiB is allocated by PyTorch, and 337.78 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-07-18 00:56:26,157 | debug_logger | ERROR | Traceback:
Traceback (most recent call last):
  File "C:\SDXL_ArtGen\bigapp.py", line 735, in generate_image
    image = pipeline(
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\utils\_contextlib.py", line 115, in decorate_context
    return func(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\pipelines\stable_diffusion_xl\pipeline_stable_diffusion_xl.py", line 1216, in __call__
    noise_pred = self.unet(
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\accelerate\hooks.py", line 165, in new_forward
    output = module._old_forward(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\models\unet_2d_condition.py", line 1177, in forward
    sample = upsample_block(
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\models\unet_2d_blocks.py", line 2354, in forward
    hidden_states = attn(
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\models\transformer_2d.py", line 392, in forward
    hidden_states = block(
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\models\attention.py", line 393, in forward
    ff_output = self.ff(norm_hidden_states, scale=lora_scale)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\models\attention.py", line 665, in forward
    hidden_states = module(hidden_states, scale)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\diffusers\models\activations.py", line 102, in forward
    hidden_states, gate = self.proj(hidden_states, *args).chunk(2, dim=-1)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\SDXL_ArtGen\venv\lib\site-packages\peft\tuners\lora\layer.py", line 319, in forward
    result += lora_B(lora_A(dropout(x))) * scaling
torch.cuda.OutOfMemoryError: CUDA out of memory. Tried to allocate 160.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 5.37 GiB is allocated by PyTorch, and 337.78 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)

2025-07-18 00:56:26,157 | debug_logger | ERROR | 
=== Generation Error ===
2025-07-18 00:56:26,157 | debug_logger | ERROR | Error type: OutOfMemoryError
2025-07-18 00:56:26,158 | debug_logger | ERROR | Error message: CUDA out of memory. Tried to allocate 160.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 5.37 GiB is allocated by PyTorch, and 337.78 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
